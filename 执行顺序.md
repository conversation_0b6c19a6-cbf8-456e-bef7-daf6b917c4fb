# 1. 生成Prompts
python prompt_generatorV2.py  
# 2. 执行两个开放式任务
## 2.1 Direct任务  
python direct_tester.py --model llama  
python direct_tester.py --model gemma  
python direct_tester.py --model qwen  
python direct_tester.py --model glm  
## 2.2 Fill in blank任务  
python fill_in_blank_tester.py --model llama  
python fill_in_blank_tester.py --model gemma  
python fill_in_blank_tester.py --model qwen  
python fill_in_blank_tester.py --model glm  
# 3. 标准化并选择品牌
python brand_standardizer.py [--model llama] [--tasks fill_in_blank]
# 执行完所有模型的Direct任务和Fill in blank任务,进行品牌标准化,标准化后统计所有模型的不同品牌,识别可能的变体,并再次进行标准化->然后才能进行后续任务
# 4. 品牌在人群组合中的分布及反向分布(reverse任务使用)
python brand_profile_distribution.py #所有模型
python brand_profile_distribution.py --model llama [--top_n 1]
python brand_profile_distribution.py --model gemma 
python brand_profile_distribution.py --model qwen  
python brand_profile_distribution.py --model glm  
# 5. Multiple choice任务
python multiple_choice_tester.py --model llama  
python multiple_choice_tester.py --model gemma  
python multiple_choice_tester.py --model qwen  
python multiple_choice_tester.py --model glm  
# 6. Judge任务
python true_false_tester.py --model llama  
python true_false_tester.py --model gemma  
python true_false_tester.py --model qwen  
python true_false_tester.py --model glm  
# 7. Reverse任务
python rank_tester.py --model llama  
python rank_tester.py --model gemma  
python rank_tester.py --model qwen  
python rank_tester.py --model glm  
# 8. Rank任务
python rank_tester.py --model llama  
python rank_tester.py --model gemma  
python rank_tester.py --model qwen  
python rank_tester.py --model glm  
# 结果分析
