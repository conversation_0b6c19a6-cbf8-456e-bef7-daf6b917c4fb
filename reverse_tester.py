import pandas as pd
import os
import re
import time
import json
import logging
import tqdm
import ollama
import random
import math
import asyncio
from datetime import datetime
import argparse
from log_config import setup_logger
from collections import Counter
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL
# import brand_profile_analyzer  # 导入品牌分析工具

# 配置日志
logger = setup_logger("reverse_experiment.log")


# 解析命令行参数
parser = argparse.ArgumentParser(description='Reverse任务测试程序')
parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                    help=get_models_help_text())
parser.add_argument('--stats_file', type=str, help='指定包含品牌分布统计信息的JSON文件路径')
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
parser.add_argument('--batch_size', type=int, default=40,
                    help='每批处理的提示数量，默认为40')
args = parser.parse_args()

# 根据参数选择模型
MODEL_NAME = get_model_name(args.model)

# 配置常量
NUM_REPEATS = 10  # 每个提示测试的次数
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)
REVERSE_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "reverse")
MAX_CONCURRENCY = args.concurrency  # 最大并发请求数

TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# 文件路径模板
REVERSE_OUTPUT_FILE_TEMPLATE = os.path.join(REVERSE_OUTPUT_DIR, f"results_{{}}.csv")
REVERSE_FINAL_OUTPUT_FILE = os.path.join(REVERSE_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
ACCURACY_STATS_FILE = os.path.join(REVERSE_OUTPUT_DIR, f"accuracy_stats_{TIMESTAMP}.json")
PROMPT_FILE = "data/wearing_prompts_V4_reverse.csv"  # 使用reverse专用提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）

# 定义可能的属性值
AGE_VALUES = ['young', 'middle-aged', 'old']
GENDER_VALUES = ['male', 'female']
RACE_VALUES = ['white', 'black', 'asian', 'hispanic']

def load_json_file(file_path, file_description="文件"):
    """通用的JSON文件加载函数"""
    if not os.path.exists(file_path):
        logger.error(f"{file_description}不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        logger.info(f"成功加载{file_description}: {file_path}")
        return data
    except Exception as e:
        logger.error(f"加载{file_description}时出错: {e}")
        return None

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def parse_reverse_response(response):
    """
    解析反向推理的响应，提取年龄、性别和种族信息
    
    参数:
        response: 模型原始响应
    
    返回:
        tuple: (age, gender, race) 三个属性值
    """
    # 清理响应文本（去除多余空格、标点符号等）
    clean_response = response.strip().lower()
    
    # 移除末尾的标点符号
    if clean_response.endswith(('.', ',', ':', ';')):
        clean_response = clean_response[:-1]
    
    # 尝试按逗号分割
    parts = [part.strip() for part in clean_response.split(',')]
    
    # 如果分割后不是3个部分，尝试其他分隔符或格式
    if len(parts) != 3:
        # 尝试按空格或下划线分割
        parts = [part.strip() for part in re.split(r'[_\s]+', clean_response)]
        
        # 如果仍然不是3个部分，进行更复杂的解析
        if len(parts) != 3:
            # 尝试提取关键词
            age = next((age for age in AGE_VALUES if age in clean_response), None)
            gender = next((gender for gender in GENDER_VALUES if gender in clean_response), None)
            race = next((race for race in RACE_VALUES if race in clean_response), None)
            
            return (age, gender, race)
    
    # 如果成功分割为3个部分，尝试识别每个部分
    if len(parts) == 3:
        # 默认假设顺序是 age, gender, race
        age, gender, race = parts
        
        # 验证并修正每个属性
        if age not in AGE_VALUES:
            age = next((age_val for age_val in AGE_VALUES if age_val in age), None)
            
        if gender not in GENDER_VALUES:
            gender = next((gender_val for gender_val in GENDER_VALUES if gender_val in gender), None)
            
        if race not in RACE_VALUES:
            race = next((race_val for race_val in RACE_VALUES if race_val in race), None)
        
        return (age, gender, race)
    
    # 如果无法解析，返回None
    return (None, None, None)

def select_best_attribute_value(values, possible_values):
    """
    从多个值中选择最佳的属性值
    
    参数:
        values: 属性值列表
        possible_values: 可能的有效值列表
    
    返回:
        最佳属性值
    """
    # 过滤掉None值和不在可能值列表中的值
    filtered_values = [v for v in values if v and v in possible_values]
    
    # 如果没有有效值，返回None
    if not filtered_values:
        return None
    
    # 统计每个值的出现频率
    value_counts = Counter(filtered_values)
    
    # 按频率降序排序，频率相同时按字母顺序排序
    sorted_values = sorted(value_counts.items(), key=lambda x: (-x[1], x[0]))
    
    # 返回出现频率最高的值
    return sorted_values[0][0] if sorted_values else None

def select_best_response(responses):
    """
    从多个响应中选择最佳的年龄、性别和种族值
    
    参数:
        responses: 模型原始响应列表
    
    返回:
        tuple: (age, gender, race) 三个属性的最佳值
    """
    parsed_responses = [parse_reverse_response(response) for response in responses]
    
    # 提取每个属性的所有值
    ages = [age for age, _, _ in parsed_responses if age]
    genders = [gender for _, gender, _ in parsed_responses if gender]
    races = [race for _, _, race in parsed_responses if race]
    
    # 选择每个属性的最佳值
    best_age = select_best_attribute_value(ages, AGE_VALUES)
    best_gender = select_best_attribute_value(genders, GENDER_VALUES)
    best_race = select_best_attribute_value(races, RACE_VALUES)
    
    return (best_age, best_gender, best_race)

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 随着重试次数增加等待时间

def save_batch_results(reverse_results, batch_idx):
    """保存批次结果"""
    if reverse_results:
        df = pd.DataFrame(reverse_results)
        output_file = REVERSE_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        df.to_csv(output_file, index=False)
        logger.info(f"已保存 {len(df)} 条reverse结果到 {output_file}")

def merge_reverse_results():
    """合并所有reverse批次结果为最终reverse结果文件"""
    reverse_files = [f for f in os.listdir(REVERSE_OUTPUT_DIR) if f.startswith(f'results_') and f.endswith('.csv') and not f.startswith(f'final_')]
    
    if reverse_files:
        dfs = [pd.read_csv(os.path.join(REVERSE_OUTPUT_DIR, f)) for f in reverse_files]
        if dfs:
            combined = pd.concat(dfs, ignore_index=True)
            combined.to_csv(REVERSE_FINAL_OUTPUT_FILE, index=False)
            logger.info(f"已合并 {len(combined)} 条reverse结果到 {REVERSE_FINAL_OUTPUT_FILE}")
            return combined
    return None

def load_brand_profiles():
    """从统计文件加载品牌及其对应的人群画像，排除'none'人群画像"""
    # 确定品牌分布统计文件路径
    if args.stats_file:
        stats_file = args.stats_file
    else:
        stats_output_dir = os.path.join(MODEL_OUTPUT_DIR)
        if not os.path.exists(stats_output_dir):
            logger.warning(f"模型结果目录不存在: {stats_output_dir}")
            return {}
        stats_file = os.path.join(stats_output_dir, 'brand_distribution_stats.json')
    
    # 加载品牌分布统计文件
    stats = load_json_file(stats_file, "品牌分布统计")
    if not stats:
        return {}
    
    # 从统计信息中提取所有唯一的Top品牌及其对应的人群画像，排除'none'
    brand_to_profiles = {}
    for profile, profile_data in stats.items():
        # 跳过'none'人群画像
        if profile == 'none':
            continue
            
        for brand in profile_data['top_brands']:
            if brand not in brand_to_profiles:
                brand_to_profiles[brand] = []
            brand_to_profiles[brand].append(profile)
    
    # 记录排除'none'后的品牌数量
    logger.info(f"共加载了 {len(brand_to_profiles)} 个唯一品牌（已排除'none'人群画像）")
    return brand_to_profiles

def load_base_prompts():
    """加载reverse任务的基础提示词"""
    try:
        # 读取提示文件
        prompts_df = pd.read_csv(PROMPT_FILE)
        
        # 去除重复的occasion_season组合，确保只有16个基础提示
        base_prompts = prompts_df.drop_duplicates(subset=['occasion', 'season']).copy()
        
        logger.info(f"成功加载 {len(base_prompts)} 个reverse基础提示词")
        return base_prompts.to_dict('records')
    except Exception as e:
        logger.error(f"读取提示文件时发生错误: {e}")
        return []

def generate_reverse_prompts():
    """生成反向推理提示词"""
    # 加载品牌及其对应的人群画像
    brand_to_profiles = load_brand_profiles()
    if not brand_to_profiles:
        logger.error("无法加载品牌分布信息，无法生成reverse提示")
        return []
    
    # 加载基础提示词
    base_prompts = load_base_prompts()
    if not base_prompts:
        logger.error("无法加载基础提示词，无法生成reverse提示")
        return []
    
    reverse_prompts = []
    
    # 为每个品牌生成所有基础提示词的变体
    for brand, profiles in brand_to_profiles.items():
        for base_prompt in base_prompts:
            # 复制基础提示词条目
            prompt_entry = base_prompt.copy()
            
            # 替换品牌占位符
            prompt_entry['prompt'] = prompt_entry['prompt'].replace("{BRAND}", brand)
            prompt_entry['brand'] = brand
            prompt_entry['original_profiles'] = ','.join(profiles)  # 记录品牌对应的原始人群画像
            
            reverse_prompts.append(prompt_entry)
    
    logger.info(f"为 {len(brand_to_profiles)} 个品牌生成了 {len(reverse_prompts)} 条reverse提示词")
    return reverse_prompts, brand_to_profiles

async def process_prompt_async(prompt_entry, client, semaphore):
    """异步处理单个提示"""
    prompt_text = prompt_entry['prompt']
    brand = prompt_entry['brand']
    occasion = prompt_entry['occasion']
    season = prompt_entry['season']
    original_profiles = prompt_entry.get('original_profiles', '')
    
    # 运行多次并收集响应
    raw_responses = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                # 调用模型获取响应
                current_raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                
                # 记录原始响应
                raw_responses.append(current_raw_response)
                
            except Exception as e:
                logger.error(f"处理reverse提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not any(raw_responses):
        logger.warning(f"没有为reverse提示收集到有效响应: {prompt_text}")
        return None
    
    # 选择最佳响应
    best_age, best_gender, best_race = select_best_response(raw_responses)
    
    # 创建结果字典
    result = {
        'prompt_type': 'reverse',
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text,
        'brand': brand,
        'original_profiles': original_profiles,  # 记录品牌对应的原始人群画像
        'predicted_age': best_age or 'unknown', 
        'predicted_gender': best_gender or 'unknown',
        'predicted_race': best_race or 'unknown'
    }
    
    # 添加原始响应
    for i, raw in enumerate(raw_responses):
        result[f'raw_response_{i+1}'] = raw
    
    return result

async def process_batch_async(batch_prompts, batch_idx, client, semaphore):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_prompts)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = []
    
    # 为每个提示创建任务
    for prompt_entry in batch_prompts:
        tasks.append(process_prompt_async(prompt_entry, client, semaphore))
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm.tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(tasks) if len(tasks) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

async def run_reverse_test():
    """运行反向推理测试"""
    # 确保输出目录存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(REVERSE_OUTPUT_DIR)
    
    # 生成反向推理提示词
    reverse_prompts, brand_to_profiles = generate_reverse_prompts()
    if not reverse_prompts:
        logger.error("无法生成reverse提示词，无法执行reverse任务")
        return
    
    # 显示部分品牌及其对应的人群画像
    logger.info(f"将使用 {len(brand_to_profiles)} 个唯一品牌进行reverse测试")
    for brand, profiles in list(brand_to_profiles.items())[:5]:  # 只显示前5个作为示例
        logger.info(f"品牌 '{brand}' 对应的人群画像: {', '.join(profiles)}")
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    # 计算批次数量和创建批次
    batch_size = args.batch_size
    batches = [reverse_prompts[i:i + batch_size] for i in range(0, len(reverse_prompts), batch_size)]
    num_batches = len(batches)
    
    # 存储所有结果
    all_results = []
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(MAX_CONCURRENCY)
    
    # 使用tqdm显示总体进度
    with tqdm.tqdm(total=num_batches, desc="Reverse任务进度") as pbar_batches:
        # 遍历每个批次
        for batch_idx, batch_prompts in enumerate(batches):
            logger.info(f"Reverse批次 {batch_idx+1}/{num_batches}: {len(batch_prompts)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(batch_prompts, batch_idx, client, semaphore)
                
                # 添加到所有结果中
                all_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理批次 {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 合并结果并保存
    merged_results = merge_reverse_results()
    
    # 计算准确率统计
    if merged_results is not None:
        calculate_accuracy_stats(merged_results)
    
    return all_results

def calculate_accuracy_stats(results_df):
    """计算准确率统计信息"""
    # 统计总样本数量
    total_samples = len(results_df)
    
    # 创建统计结果字典
    stats = {
        'total_samples': total_samples,
        'age_accuracy': 0,
        'gender_accuracy': 0,
        'race_accuracy': 0,
        'overall_accuracy': 0,
        'detailed': {
            'age': {},
            'gender': {},
            'race': {}
        }
    }
    
    # 遍历每个结果，提取原始profile和预测的属性
    correct_predictions = {'age': 0, 'gender': 0, 'race': 0, 'all': 0}
    
    # 详细统计每个属性预测结果
    age_stats = Counter()
    gender_stats = Counter()
    race_stats = Counter()
    
    # 计算结果只有在原始画像包含所有三个属性时才有意义
    valid_samples = 0
    
    for _, row in results_df.iterrows():
        # 跳过没有原始画像或预测值的行
        if 'original_profiles' not in row or not row['original_profiles']:
            continue
            
        # 获取预测结果
        predicted_age = str(row['predicted_age']).lower() if 'predicted_age' in row else 'unknown'
        predicted_gender = str(row['predicted_gender']).lower() if 'predicted_gender' in row else 'unknown'
        predicted_race = str(row['predicted_race']).lower() if 'predicted_race' in row else 'unknown'
        
        # 解析原始画像，格式可能是 "young_male_white,young_female_white" 等
        original_profiles = row['original_profiles'].split(',')
        
        # 从原始画像中提取各属性值的频率
        profile_ages = []
        profile_genders = []
        profile_races = []
        
        for profile in original_profiles:
            parts = profile.split('_')
            if len(parts) == 3:  # 确保是完整的人群画像
                profile_ages.append(parts[0].lower())
                profile_genders.append(parts[1].lower())
                profile_races.append(parts[2].lower())
        
        # 如果至少有一个有效画像
        if profile_ages and profile_genders and profile_races:
            valid_samples += 1
            
            # 统计每个属性预测结果
            age_stats[predicted_age] += 1
            gender_stats[predicted_gender] += 1
            race_stats[predicted_race] += 1
            
            # 检查预测是否正确
            if predicted_age in profile_ages:
                correct_predictions['age'] += 1
            
            if predicted_gender in profile_genders:
                correct_predictions['gender'] += 1
                
            if predicted_race in profile_races:
                correct_predictions['race'] += 1
                
            # 检查所有属性是否都正确
            if (predicted_age in profile_ages and 
                predicted_gender in profile_genders and 
                predicted_race in profile_races):
                correct_predictions['all'] += 1
    
    # 计算准确率
    if valid_samples > 0:
        stats['age_accuracy'] = correct_predictions['age'] / valid_samples
        stats['gender_accuracy'] = correct_predictions['gender'] / valid_samples
        stats['race_accuracy'] = correct_predictions['race'] / valid_samples
        stats['overall_accuracy'] = correct_predictions['all'] / valid_samples
        
        # 添加详细统计
        stats['detailed']['age'] = {k: v / valid_samples for k, v in age_stats.items()}
        stats['detailed']['gender'] = {k: v / valid_samples for k, v in gender_stats.items()}
        stats['detailed']['race'] = {k: v / valid_samples for k, v in race_stats.items()}
        
        stats['valid_samples'] = valid_samples
    
    # 保存统计结果
    try:
        with open(ACCURACY_STATS_FILE, 'w') as f:
            json.dump(stats, f, indent=2)
        logger.info(f"已保存准确率统计结果到 {ACCURACY_STATS_FILE}")
        
        # 输出主要统计结果
        logger.info(f"有效样本数量: {valid_samples}")
        logger.info(f"年龄准确率: {stats['age_accuracy']:.2f}")
        logger.info(f"性别准确率: {stats['gender_accuracy']:.2f}")
        logger.info(f"种族准确率: {stats['race_accuracy']:.2f}")
        logger.info(f"总体准确率: {stats['overall_accuracy']:.2f}")
    except Exception as e:
        logger.error(f"保存准确率统计结果时发生错误: {e}")
    
    return stats

async def main_async():
    """异步主函数"""    
    print(f"Reverse任务测试配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {args.batch_size}")
    print(f"- 最大并发数: {MAX_CONCURRENCY}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {REVERSE_OUTPUT_DIR}")
    if args.stats_file:
        print(f"- 使用指定统计文件: {args.stats_file}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始Reverse实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {args.batch_size}, 并发数: {MAX_CONCURRENCY}")
    logger.info(f"实验结果将保存到目录: {REVERSE_OUTPUT_DIR}")
    
    # 运行反向推理测试
    await run_reverse_test()
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nReverse实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("Reverse实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()