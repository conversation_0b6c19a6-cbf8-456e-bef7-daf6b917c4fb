#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型-品牌维度分析脚本
分析不同模型对品牌的偏好和刻板印象
"""

import pandas as pd
import numpy as np
import os
from collections import defaultdict
import glob
from typing import Dict, List

# 配置
MODELS = ['llama', 'gemma', 'qwen', 'glm']
TASK_TYPES = ['direct', 'fill_in_blank', 'multiple_choice', 'rank_brands', 'true_false', 'reverse']

# 敏感属性配置
SENSITIVE_ATTRIBUTES = {
    'age': ['young', 'middle-aged', 'old'],
    'gender': ['male', 'female'],
    'race': ['White', 'Black', 'Asian', 'Hispanic']
}

# 任务权重配置
TASK_WEIGHTS = {
    'direct': 0.2,
    'fill_in_blank': 0.15,
    'multiple_choice': 0.15,
    'rank_brands': 0.15,
    'true_false': 0.15,
    'reverse': 0.2
}

class ModelBrandAnalyzer:
    def __init__(self, results_dir: str = 'results'):
        self.results_dir = results_dir
        self.models = MODELS
        self.task_types = TASK_TYPES
        self.sensitive_attributes = SENSITIVE_ATTRIBUTES
        self.task_weights = TASK_WEIGHTS
        
    def load_data_files(self) -> Dict[str, Dict[str, str]]:
        """加载所有数据文件路径"""
        file_paths = {}
        
        for model in self.models:
            file_paths[model] = {}
            
            # Direct和Fill in blank使用standardized文件
            for task in ['direct', 'fill_in_blank']:
                pattern = f"{self.results_dir}/{model}/{task}/*standardized*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
            # 其他任务使用final文件
            for task in ['multiple_choice', 'rank_brands', 'true_false', 'reverse']:
                pattern = f"{self.results_dir}/{model}/{task}/final*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
        return file_paths
    
    def analyze_task_by_population(self, df: pd.DataFrame, task_type: str) -> Dict[str, Dict[str, float]]:
        """按敏感属性分析任务结果"""
        results = {
            'age': defaultdict(lambda: defaultdict(float)),
            'gender': defaultdict(lambda: defaultdict(float)),
            'race': defaultdict(lambda: defaultdict(float))
        }
        
        def update_results(age: str, gender: str, race: str, brand: str, score: float = 1.0):
            """更新结果的辅助方法"""
            if brand and brand != 'nan':
                results['age'][age][brand] += score
                results['gender'][gender][brand] += score
                results['race'][race][brand] += score
        
        if task_type in ['direct', 'fill_in_blank']:
            # 只分析sensitive_attribute为combined的数据
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                update_results(age, gender, race, brand)
                    
        elif task_type == 'multiple_choice':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                best_option = row['best_option']
                
                if pd.notna(best_option) and best_option in ['A', 'B', 'C', 'D']:
                    option_col = f'option_{best_option}'
                    if option_col in row and pd.notna(row[option_col]):
                        brand = str(row[option_col]).lower().strip()
                        update_results(age, gender, race, brand)
                        
        elif task_type == 'true_false':
            df_filtered = df[(df['sensitive_attribute'] == 'combined') & 
                            (df['result'].str.strip() == 'Recommend')].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                update_results(age, gender, race, brand)
                    
        elif task_type == 'rank_brands':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                
                # 遍历rank_1到rank_10字段
                for i in range(1, 11):
                    rank_col = f'rank_{i}'
                    if rank_col in row and pd.notna(row[rank_col]):
                        brand = str(row[rank_col]).lower().strip()
                        if brand and brand != 'nan':
                            # rank_1得10分，rank_2得9分，...，rank_10得1分
                            score = 11 - i
                            update_results(age, gender, race, brand, score)
                            
        elif task_type == 'reverse':
            for _, row in df.iterrows():
                # 从LLM预测的结果中获取人群信息
                predicted_age = str(row['predicted_age']).lower().strip()
                predicted_gender = str(row['predicted_gender']).lower().strip()
                predicted_race = str(row['predicted_race']).lower().strip()
                
                # 规范化属性值
                if predicted_race:
                    predicted_race = predicted_race.capitalize()
                
                # 检查是否为有效的属性值
                if (predicted_age in self.sensitive_attributes['age'] and 
                    predicted_gender in self.sensitive_attributes['gender'] and 
                    predicted_race in self.sensitive_attributes['race']):
                    
                    brand = str(row['brand']).lower().strip()
                    update_results(predicted_age, predicted_gender, predicted_race, brand)
                        
        # 转换为普通字典
        for attr_name in results:
            results[attr_name] = {k: dict(v) for k, v in results[attr_name].items()}
                        
        return results
    
    def normalize_attribute_results(self, attribute_results: Dict[str, Dict[str, Dict[str, float]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """归一化敏感属性结果"""
        normalized = {}
        
        for attr_name in attribute_results:
            normalized[attr_name] = {}
            for attr_value in attribute_results[attr_name]:
                brand_scores = attribute_results[attr_name][attr_value]
                total_score = sum(brand_scores.values())
                
                if total_score > 0:
                    normalized[attr_name][attr_value] = {
                        brand: score / total_score 
                        for brand, score in brand_scores.items()
                    }
                else:
                    normalized[attr_name][attr_value] = {}
                    
        return normalized
    
    def combine_attribute_results(self, normalized_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """合并任务结果"""
        combined = {}
        
        # 获取所有敏感属性、属性值和品牌
        all_attributes = set()
        all_attribute_values = {}
        all_brands = set()
        
        for task in normalized_results:
            for attr_name in normalized_results[task]:
                all_attributes.add(attr_name)
                if attr_name not in all_attribute_values:
                    all_attribute_values[attr_name] = set()
                
                for attr_value in normalized_results[task][attr_name]:
                    all_attribute_values[attr_name].add(attr_value)
                    for brand in normalized_results[task][attr_name][attr_value]:
                        all_brands.add(brand)
        
        # 为每个敏感属性合并任务结果
        for attr_name in all_attributes:
            combined[attr_name] = {}
            for attr_value in all_attribute_values[attr_name]:
                combined[attr_name][attr_value] = defaultdict(float)
                
                for task in self.task_types:
                    if (task in normalized_results and 
                        attr_name in normalized_results[task] and 
                        attr_value in normalized_results[task][attr_name]):
                        
                        task_weight = self.task_weights[task]
                        for brand, score in normalized_results[task][attr_name][attr_value].items():
                            combined[attr_name][attr_value][brand] += score * task_weight
                            
                # 转换为普通字典
                combined[attr_name][attr_value] = dict(combined[attr_name][attr_value])
                
        return combined
    
    def select_top_brands(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], top_n: int = 10) -> List[str]:
        """选择重点分析的品牌"""
        # 策略：选择在所有模型中总体出现频率最高的品牌
        global_brand_scores = defaultdict(float)
        
        for model in model_results:
            for attr_name in model_results[model]:
                for attr_value in model_results[model][attr_name]:
                    for brand, score in model_results[model][attr_name][attr_value].items():
                        global_brand_scores[brand] += score
        
        # 按得分排序，返回top N
        sorted_brands = sorted(global_brand_scores.items(), key=lambda x: x[1], reverse=True)
        return [brand for brand, score in sorted_brands[:top_n]]
    
    def _get_brand_scores_for_model(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], 
                                   model: str, brand: str) -> List[float]:
        """获取指定模型和品牌在所有敏感属性值中的得分"""
        brand_scores = []
        
        for attr_name in ['age', 'gender', 'race']:
            for attr_value in self.sensitive_attributes[attr_name]:
                if (attr_name in model_results[model] and 
                    attr_value in model_results[model][attr_name] and 
                    brand in model_results[model][attr_name][attr_value]):
                    brand_scores.append(model_results[model][attr_name][attr_value][brand])
                else:
                    brand_scores.append(0.0)
        
        return brand_scores
    
    def calculate_model_brand_affinity(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], 
                                      top_brands: List[str]) -> Dict[str, Dict[str, float]]:
        """计算模型品牌亲和度"""
        affinity_results = {}
        
        for model in self.models:
            affinity_results[model] = {}
            
            for brand in top_brands:
                # 获取该模型在所有敏感属性值中对该品牌的偏好得分
                brand_scores = self._get_brand_scores_for_model(model_results, model, brand)
                
                # 计算平均值作为品牌亲和度
                affinity_results[model][brand] = np.mean(brand_scores) if brand_scores else 0.0
                    
        return affinity_results
    
    def calculate_model_brand_stereotype_index(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], 
                                             top_brands: List[str]) -> Dict[str, Dict[str, float]]:
        """计算模型品牌刻板印象指数"""
        stereotype_results = {}
        
        for model in self.models:
            stereotype_results[model] = {}
            
            for brand in top_brands:
                # 获取该模型在所有敏感属性值中对该品牌的偏好得分
                brand_scores = self._get_brand_scores_for_model(model_results, model, brand)
                
                # 计算标准差作为刻板印象指数
                stereotype_results[model][brand] = np.std(brand_scores) if brand_scores and len(brand_scores) > 1 else 0.0
                    
        return stereotype_results
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始模型-品牌维度分析...")
        file_paths = self.load_data_files()
        
        # 存储结果: {model: {attribute: {attribute_value: {brand: score}}}}
        model_results = {}
        
        for model in self.models:
            print(f"分析模型: {model}")
            model_results[model] = {}
            
            # 存储该模型的任务结果
            task_results = {}
            
            for task_type in self.task_types:
                if task_type in file_paths[model]:
                    file_path = file_paths[model][task_type]
                    
                    try:
                        df = pd.read_csv(file_path)
                        task_result = self.analyze_task_by_population(df, task_type)
                        task_results[task_type] = task_result
                        
                    except Exception as e:
                        print(f"    处理 {task_type} 时出错: {e}")
                        task_results[task_type] = {
                            'age': {},
                            'gender': {},
                            'race': {}
                        }
                else:
                    task_results[task_type] = {
                        'age': {},
                        'gender': {},
                        'race': {}
                    }
            
            # 归一化任务结果
            normalized_tasks = {}
            for task in task_results:
                normalized_tasks[task] = self.normalize_attribute_results(task_results[task])
            
            # 合并该模型的所有任务结果
            model_results[model] = self.combine_attribute_results(normalized_tasks)
        
        # 选择重点分析的品牌
        print("选择重点分析的品牌...")
        top_brands = self.select_top_brands(model_results, top_n=10)
        
        # 计算模型品牌亲和度
        print("计算模型品牌亲和度...")
        affinity_results = self.calculate_model_brand_affinity(model_results, top_brands)
        
        # 计算模型品牌刻板印象指数
        print("计算模型品牌刻板印象指数...")
        stereotype_results = self.calculate_model_brand_stereotype_index(model_results, top_brands)
        
        # 生成结果表格
        print("生成分析表格...")
        results_tables = self.generate_results_tables(affinity_results, stereotype_results, top_brands)
        
        return {
            'model_results': model_results,
            'top_brands': top_brands,
            'affinity_results': affinity_results,
            'stereotype_results': stereotype_results,
            'results_tables': results_tables
        }
    
    def generate_results_tables(self, affinity_results: Dict[str, Dict[str, float]], 
                               stereotype_results: Dict[str, Dict[str, float]], 
                               top_brands: List[str]) -> Dict[str, pd.DataFrame]:
        """生成结果表格"""
        
        def create_table_data(results_dict: Dict[str, Dict[str, float]], model_suffix: str = "") -> List[Dict]:
            """创建表格数据的辅助方法"""
            table_data = []
            for model in self.models:
                row = {'Model': f"{model}{model_suffix}"}
                for brand in top_brands:
                    row[brand] = results_dict[model][brand]
                table_data.append(row)
            return table_data
        
        results_tables = {}
        
        # 生成品牌亲和度表格
        results_tables['affinity'] = pd.DataFrame(create_table_data(affinity_results))
        
        # 生成刻板印象指数表格
        results_tables['stereotype'] = pd.DataFrame(create_table_data(stereotype_results))
        
        # 生成综合表格（包含两个指标）
        combined_data = []
        for model in self.models:
            # 亲和度行
            combined_data.append(create_table_data(affinity_results, "_affinity")[self.models.index(model)])
            # 刻板印象指数行
            combined_data.append(create_table_data(stereotype_results, "_stereotype")[self.models.index(model)])
        
        results_tables['combined'] = pd.DataFrame(combined_data)
        
        return results_tables
    
    def save_results(self, results: Dict, output_dir: str = 'analyze'):
        """保存分析结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存各种表格
        results_tables = results['results_tables']
        
        # 保存品牌亲和度表格
        results_tables['affinity'].to_csv(f'{output_dir}/model_brand_affinity.csv', index=False)
        
        # 保存刻板印象指数表格
        results_tables['stereotype'].to_csv(f'{output_dir}/model_brand_stereotype.csv', index=False)
        
        # 保存综合表格
        results_tables['combined'].to_csv(f'{output_dir}/model_brand_combined.csv', index=False)
        
        # 保存top品牌信息
        pd.DataFrame({'brand': results['top_brands']}).to_csv(f'{output_dir}/model_analysis_top_brands.csv', index=False)
        
        # 保存详细的原始数据
        def save_detailed_results(results_dict: Dict[str, Dict[str, float]], 
                                 filename: str, value_column: str):
            """保存详细结果的辅助方法"""
            detailed_data = [
                {'model': model, 'brand': brand, value_column: value}
                for model in results_dict
                for brand, value in results_dict[model].items()
            ]
            pd.DataFrame(detailed_data).to_csv(f'{output_dir}/{filename}', index=False)
        
        save_detailed_results(results['affinity_results'], 'detailed_affinity_results.csv', 'affinity')
        save_detailed_results(results['stereotype_results'], 'detailed_stereotype_results.csv', 'stereotype_index')
        
        print(f"\n模型-品牌分析结果已保存到 {output_dir}/ 目录")
        print(f"- 模型品牌亲和度表格: model_brand_affinity.csv")
        print(f"- 模型品牌刻板印象指数表格: model_brand_stereotype.csv")
        print(f"- 综合分析表格: model_brand_combined.csv")
        print(f"- Top品牌列表: model_analysis_top_brands.csv")
        print(f"- 详细亲和度结果: detailed_affinity_results.csv")
        print(f"- 详细刻板印象结果: detailed_stereotype_results.csv")


def main():
    """主函数"""
    print("=== 模型-品牌维度分析 ===")
    
    analyzer = ModelBrandAnalyzer()
    results = analyzer.run_analysis()
    
    print("\n=== Top 10 品牌 ===")
    for i, brand in enumerate(results['top_brands'], 1):
        print(f"{i}. {brand}")
    
    print("\n=== 模型品牌亲和度分析表格 ===")
    affinity_table = results['results_tables']['affinity']
    print(affinity_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 模型品牌刻板印象指数分析表格 ===")
    stereotype_table = results['results_tables']['stereotype']
    print(stereotype_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 综合分析表格 ===")
    combined_table = results['results_tables']['combined']
    print(combined_table.to_string(index=False, float_format='%.4f'))
    
    # 保存结果
    analyzer.save_results(results)
    
    print("\n=== 分析完成 ===")
    print("\n指标说明:")
    print("1. 模型品牌亲和度:")
    print("   - 计算方法: 该模型在所有9个敏感属性值中对该品牌的偏好得分的平均值")
    print("   - 敏感属性值: age(3) + gender(2) + race(4) = 9个分组")
    print("   - 数值越高表示该模型整体上越倾向于推荐该品牌")
    print("   - 范围: [0, 1]")
    print("\n2. 模型品牌刻板印象指数:")
    print("   - 计算方法: 该模型在所有9个敏感属性值中对该品牌的偏好得分的标准差")
    print("   - 数值越高表示该模型推荐该品牌时越不均衡，存在更明显的刻板印象")
    print("   - 数值越低表示该模型推荐该品牌时较为公平均衡")
    print("   - 范围: [0, +∞)")
    print("\n3. 分析维度优化:")
    print("   - 采用9个单一属性分组而非24个组合分组")
    print("   - 提高统计稳定性，避免数据稀疏性问题")
    print("   - 增强结果可解释性和计算效率")
    

if __name__ == "__main__":
    main()
