import pandas as pd
import re
import json
import os
from collections import Counter
from difflib import SequenceMatcher
import argparse
from log_config import setup_logger
#使用示例：
#python brand_response_parser_for_mistral.py --file results/mistral/direct/final_20250728_042925.csv --output results/mistral/direct/final_clean_parsed.csv
# 配置日志
logger = setup_logger("brand_response_parser.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='品牌响应解析器 - 从原始响应中解析品牌名称')
parser.add_argument('--file', type=str, required=True, help='要处理的CSV文件路径')
parser.add_argument('--output', type=str, help='输出文件路径，默认为原文件名_parsed.csv')
args = parser.parse_args()

# 品牌名称的标准化映射
BRAND_MAPPING_FILE = "brand_mapping.json"

# 加载品牌映射
def load_brand_mapping():
    try:
        with open(BRAND_MAPPING_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载品牌映射文件时发生错误: {e}")
        return {}

BRAND_MAPPING = load_brand_mapping()

# 常见品牌列表（用于匹配）
KNOWN_BRANDS = [
    'nike', 'adidas', 'uniqlo', 'zara', 'h&m', 'ralph lauren', 'tommy hilfiger',
    'calvin klein', 'hugo boss', 'armani', 'gucci', 'prada', 'versace',
    'michael kors', 'coach', 'kate spade', 'tory burch', 'marc jacobs',
    'brooks brothers', 'banana republic', 'j.crew', 'gap', 'old navy',
    'levi\'s', 'wrangler', 'lee', 'diesel', 'true religion',
    'north face', 'patagonia', 'columbia', 'under armour', 'lululemon',
    'ann taylor', 'eileen fisher', 'talbots', 'chico\'s', 'white house black market',
    'stella mccartney', 'alexander wang', 'diane von furstenberg', 'theory',
    'madewell', 'everlane', 'reformation', 'ganni', 'acne studios',
    'burberry', 'barbour', 'stone island', 'moncler', 'canada goose',
    'polo ralph lauren', 'lacoste', 'fred perry', 'stone island', 'cp company',
    'issey miyake', 'comme des garcons', 'yohji yamamoto', 'kenzo', 'balmain',
    'saint laurent', 'bottega veneta', 'celine', 'loewe', 'givenchy',
    'balenciaga', 'off-white', 'vetements', 'acne', 'ganni', 'stussy',
    'supreme', 'palace', 'kith', 'fear of god', 'essentials', 'fog',
    'marks & spencer', 'next', 'primark', 'topshop', 'asos', 'boohoo',
    'pretty little thing', 'missguided', 'new look', 'river island',
    'mango', 'massimo dutti', 'cos', 'weekday', 'monki', 'arket',
    'muji', 'uniqlo', 'gu', 'fast retailing', 'mizuno', 'asics',
    'new balance', 'converse', 'vans', 'puma', 'reebok', 'fila',
    'champion', 'russell athletic', 'starter', 'kappa', 'umbro',
    'oakley', 'ray-ban', 'persol', 'maui jim', 'costa del mar'
]

def similar_text(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def normalize_brand(brand):
    """标准化品牌名称"""
    if not brand:
        return brand
    
    brand_lower = brand.lower().strip()
    
    # 检查映射
    if brand_lower in BRAND_MAPPING:
        return BRAND_MAPPING[brand_lower]
    
    # 返回首字母大写的品牌名称
    return ' '.join(word.capitalize() for word in brand_lower.split())

def clean_response_text(text):
    """清理响应文本，去除多余信息"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text).strip()
    
    # 移除常见的前缀和后缀
    prefixes_to_remove = [
        r'^for\s+a?\s*\w*\s*',
        r'^i\s+would\s+recommend\s*',
        r'^i\s+would\s+suggest\s*',
        r'^the\s+brand\s*',
        r'^brand:\s*',
        r'^recommendation:\s*',
        r'^answer:\s*',
        r'^response:\s*'
    ]
    
    for prefix in prefixes_to_remove:
        text = re.sub(prefix, '', text, flags=re.IGNORECASE)
    
    # 移除括号及其内容
    text = re.sub(r'\([^)]*\)', '', text)
    text = re.sub(r'\[[^\]]*\]', '', text)
    text = re.sub(r'\{[^}]*\}', '', text)
    
    # 移除引号
    text = re.sub(r'["\']', '', text)
    
    # 移除"Note:"及其后面的所有内容
    text = re.sub(r'note:.*', '', text, flags=re.IGNORECASE)
    
    # 移除常见的解释性文本
    explanatory_patterns = [
        r'for\s+their.*',
        r'known\s+for.*',
        r'they\s+offer.*',
        r'this\s+brand.*',
        r'which\s+offers.*',
        r'because.*',
        r'as\s+they.*',
        r'since.*',
        r'due\s+to.*'
    ]
    
    for pattern in explanatory_patterns:
        text = re.sub(pattern, '', text, flags=re.IGNORECASE)
    
    # 移除多余空格和标点
    text = re.sub(r'\s+', ' ', text).strip()
    text = re.sub(r'^[,.\-\s]+|[,.\-\s]+$', '', text)
    
    return text

def extract_brand_from_response(response):
    """从响应中提取品牌名称"""
    if not response or pd.isna(response):
        return ""

    # 清理响应文本
    cleaned = clean_response_text(response)

    if not cleaned:
        return ""

    # 尝试直接匹配已知品牌
    cleaned_lower = cleaned.lower()

    # 首先检查是否直接匹配已知品牌
    best_brand_match = None
    best_brand_score = 0

    for brand in KNOWN_BRANDS:
        brand_lower = brand.lower()

        # 完整匹配检查
        if brand_lower in cleaned_lower:
            # 确保是完整的品牌名称匹配，而不是部分匹配
            brand_words = brand_lower.split()
            if len(brand_words) == 1:
                # 单词品牌，检查边界
                pattern = r'\b' + re.escape(brand_lower) + r'\b'
                if re.search(pattern, cleaned_lower):
                    return normalize_brand(brand)
            else:
                # 多词品牌，检查完整匹配
                if brand_lower in cleaned_lower:
                    return normalize_brand(brand)

        # 相似度匹配检查
        similarity = similar_text(cleaned_lower, brand_lower)
        if similarity > best_brand_score and similarity > 0.8:  # 提高阈值
            best_brand_score = similarity
            best_brand_match = brand

    if best_brand_match:
        return normalize_brand(best_brand_match)

    # 如果没有直接匹配，尝试提取第一个看起来像品牌的词组
    # 移除常见的非品牌词汇
    words = cleaned.split()
    filtered_words = []

    skip_words = {
        'for', 'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'of',
        'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
        'after', 'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were',
        'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will',
        'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
        'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
        'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
        'comfortable', 'stylish', 'professional', 'casual', 'formal', 'sporty',
        'elegant', 'modern', 'classic', 'trendy', 'fashionable', 'suitable',
        'appropriate', 'perfect', 'ideal', 'excellent', 'great', 'good', 'best',
        'collection', 'line', 'brand', 'clothing', 'wear', 'outfit', 'attire',
        'wardrobe', 'style', 'fashion', 'design', 'quality', 'option', 'choice',
        'summer', 'winter', 'spring', 'autumn', 'fall', 'season', 'seasonal',
        'young', 'old', 'middle-aged', 'elderly', 'senior', 'mature', 'adult',
        'male', 'female', 'man', 'woman', 'men', 'women', 'person', 'individual',
        'white', 'black', 'asian', 'hispanic', 'latino', 'latina', 'african',
        'american', 'european', 'race', 'ethnic', 'cultural', 'culture',
        'office', 'work', 'business', 'formal', 'casual', 'sports', 'athletic',
        'occasion', 'event', 'setting', 'environment', 'workplace', 'professional',
        'recommend', 'suggest', 'choice', 'option', 'selection', 'pick', 'choose',
        'id', 'suggest', 'look', 'event', 'recommend'
    }

    for word in words:
        word_clean = re.sub(r'[^\w\s]', '', word).lower()
        if word_clean and word_clean not in skip_words and len(word_clean) > 1:
            # 额外检查：避免包含描述性词汇的组合
            if not any(desc in word_clean for desc in ['aged', 'year', 'season', 'occasion', 'event', 'look']):
                filtered_words.append(word)

    # 取前1-2个词作为潜在品牌名（减少词数以避免描述性文本）
    if filtered_words:
        potential_brand = ' '.join(filtered_words[:2])

        # 检查潜在品牌是否看起来像真正的品牌名
        # 避免包含明显的描述性词汇
        descriptive_patterns = [
            r'\b(summer|winter|spring|autumn|fall)\b',
            r'\b(young|old|middle|aged|elderly|senior)\b',
            r'\b(male|female|man|woman|men|women)\b',
            r'\b(white|black|asian|hispanic|latino)\b',
            r'\b(office|work|business|formal|casual|sports)\b',
            r'\b(recommend|suggest|choice|option)\b',
            r'\b(appropriate|suitable|perfect|ideal)\b'
        ]

        is_descriptive = any(re.search(pattern, potential_brand.lower()) for pattern in descriptive_patterns)

        if not is_descriptive:
            # 检查是否与已知品牌相似
            best_match = None
            best_similarity = 0.7  # 相似度阈值

            for brand in KNOWN_BRANDS:
                similarity = similar_text(potential_brand, brand)
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = brand

            if best_match:
                return normalize_brand(best_match)
            else:
                # 如果没有找到相似的已知品牌，但看起来像品牌名，返回它
                return normalize_brand(potential_brand)

    # 如果都没有找到合适的品牌，返回空字符串
    return ""

def select_best_brand_from_parsed(parsed_brands):
    """从解析出的品牌列表中选择最佳品牌"""
    # 过滤空值
    valid_brands = [brand for brand in parsed_brands if brand and brand.strip()]
    
    if not valid_brands:
        return ""
    
    # 统计频率
    brand_counts = Counter(valid_brands)
    
    # 返回出现频率最高的品牌
    most_common = brand_counts.most_common(1)
    return most_common[0][0] if most_common else valid_brands[0]

def process_csv_file(input_file, output_file=None):
    """处理CSV文件，解析品牌响应"""
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        logger.info(f"成功读取文件 {input_file}，包含 {len(df)} 行数据")
        
        # 检查是否有raw_response列
        raw_response_cols = [col for col in df.columns if col.startswith('raw_response_')]
        if not raw_response_cols:
            logger.error("未找到raw_response列")
            return None
        
        logger.info(f"找到 {len(raw_response_cols)} 个原始响应列")
        
        # 为每个raw_response列创建对应的parsed_brand列
        for i, col in enumerate(raw_response_cols, 1):
            parsed_col = f'parsed_brand_{i}'
            logger.info(f"处理 {col} -> {parsed_col}")
            
            df[parsed_col] = df[col].apply(extract_brand_from_response)
        
        # 重新计算brand列
        parsed_brand_cols = [f'parsed_brand_{i}' for i in range(1, len(raw_response_cols) + 1)]
        
        logger.info("重新计算brand列...")
        df['brand'] = df[parsed_brand_cols].apply(
            lambda row: select_best_brand_from_parsed(row.tolist()), axis=1
        )
        
        # 重新排列列的顺序
        base_cols = ['prompt_type', 'sensitive_attribute', 'age', 'gender', 'race', 
                    'occasion', 'season', 'prompt', 'brand']
        parsed_cols = parsed_brand_cols
        raw_cols = raw_response_cols
        
        new_column_order = base_cols + parsed_cols + raw_cols
        df = df[new_column_order]
        
        # 保存结果
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_parsed.csv"
        
        df.to_csv(output_file, index=False)
        logger.info(f"已保存解析结果到 {output_file}")
        
        # 统计解析结果
        total_responses = len(df) * len(raw_response_cols)
        parsed_responses = sum(1 for col in parsed_brand_cols 
                             for val in df[col] if val and val.strip())
        
        logger.info(f"解析统计:")
        logger.info(f"  总响应数: {total_responses}")
        logger.info(f"  成功解析: {parsed_responses}")
        logger.info(f"  解析率: {parsed_responses/total_responses*100:.1f}%")
        
        # 统计品牌频率
        brand_counts = Counter(df['brand'].dropna())
        logger.info(f"解析出的品牌统计 (前10):")
        for brand, count in brand_counts.most_common(10):
            logger.info(f"  {brand}: {count}")
        
        return output_file
        
    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")
        return None

def generate_analysis_report(csv_file):
    """生成品牌分析报告"""
    try:
        df = pd.read_csv(csv_file)

        # 只分析combined数据
        combined_df = df[df['sensitive_attribute'] == 'combined']

        print(f"\n=== 品牌响应解析分析报告 ===")
        print(f"总记录数: {len(df)}")
        print(f"Combined记录数: {len(combined_df)}")

        # 原始brand列统计
        print(f"\n--- 原始Brand列统计 (前15) ---")
        original_brand_counts = Counter(combined_df['brand'].dropna())
        for brand, count in original_brand_counts.most_common(15):
            print(f"  {brand}: {count}")

        # 解析后的品牌统计
        print(f"\n--- 解析改进对比 ---")

        # 统计每个原始响应的解析结果
        parsed_brand_cols = [col for col in df.columns if col.startswith('parsed_brand_')]

        # 计算解析前后的差异
        changes = 0
        improvements = []

        for _, row in combined_df.iterrows():
            original_brand = row['brand']
            parsed_brands = [row[col] for col in parsed_brand_cols if pd.notna(row[col]) and row[col].strip()]

            if parsed_brands:
                # 使用频率最高的解析品牌
                parsed_brand_counts = Counter(parsed_brands)
                most_common_parsed = parsed_brand_counts.most_common(1)[0][0]

                if original_brand != most_common_parsed:
                    changes += 1
                    improvements.append({
                        'original': original_brand,
                        'parsed': most_common_parsed,
                        'raw_responses': [row[f'raw_response_{i}'] for i in range(1, 11)]
                    })

        print(f"发现 {changes} 处品牌解析改进")

        # 显示一些改进示例
        if improvements:
            print(f"\n--- 解析改进示例 (前5个) ---")
            for i, improvement in enumerate(improvements[:5], 1):
                print(f"{i}. 原始: '{improvement['original']}' -> 解析: '{improvement['parsed']}'")
                # 显示第一个原始响应作为示例
                if improvement['raw_responses'][0]:
                    sample_response = improvement['raw_responses'][0][:100] + "..." if len(improvement['raw_responses'][0]) > 100 else improvement['raw_responses'][0]
                    print(f"   示例响应: {sample_response}")

        # 重新计算品牌频率（基于解析结果）
        print(f"\n--- 基于解析结果的品牌频率统计 (前15) ---")
        all_parsed_brands = []
        for _, row in combined_df.iterrows():
            parsed_brands = [row[col] for col in parsed_brand_cols if pd.notna(row[col]) and row[col].strip()]
            if parsed_brands:
                # 选择频率最高的品牌
                brand_counts = Counter(parsed_brands)
                most_common = brand_counts.most_common(1)[0][0]
                all_parsed_brands.append(most_common)

        parsed_brand_counts = Counter(all_parsed_brands)
        for brand, count in parsed_brand_counts.most_common(15):
            print(f"  {brand}: {count}")

        return parsed_brand_counts

    except Exception as e:
        logger.error(f"生成分析报告时发生错误: {e}")
        return None

def main():
    """主函数"""
    print("品牌响应解析器")
    print(f"输入文件: {args.file}")

    if not os.path.exists(args.file):
        print(f"错误: 文件 {args.file} 不存在")
        return

    output_file = process_csv_file(args.file, args.output)

    if output_file:
        print(f"解析完成! 结果已保存到: {output_file}")

        # 生成分析报告
        brand_counts = generate_analysis_report(output_file)

        if brand_counts:
            # 保存改进后的品牌统计
            output_dir = os.path.dirname(output_file)
            stats_file = os.path.join(output_dir, "brand_statistics_improved.json")

            brand_stats = dict(brand_counts.most_common())
            with open(stats_file, 'w') as f:
                json.dump(brand_stats, f, indent=2)
            print(f"\n改进后的品牌统计已保存到: {stats_file}")

            # 选择前10个品牌
            selected_brands = [brand for brand, _ in brand_counts.most_common(10)]
            selected_file = os.path.join(output_dir, "selected_brands_improved.json")

            with open(selected_file, 'w') as f:
                json.dump(selected_brands, f, indent=2)
            print(f"改进后的选定品牌列表已保存到: {selected_file}")

    else:
        print("解析失败!")

if __name__ == "__main__":
    main()
