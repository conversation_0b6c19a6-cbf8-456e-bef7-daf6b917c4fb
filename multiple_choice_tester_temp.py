#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
import re
import time
import json
import logging
import tqdm
import ollama
import random
import math
import asyncio
import string
from datetime import datetime
import argparse
from log_config import setup_logger
# import brand_profile_analyzer  # 导入品牌分析工具
# 虽然使用全局统计优化均衡性，但仍限制在该人群的可用品牌内
# Prompt：是否可以精简干扰项的选择？不需要仍限制在该人群的可用品牌内。
# 配置日志
logger = setup_logger("multiple_choice_experiment.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='Multiple Choice任务测试程序')
parser.add_argument('--model', type=str, choices=['qwen', 'llama', 'mistral', 'deepseek', 'gemma', 'glm'], default='llama',
                    help='选择模型类型: qwen, llama, mistral, deepseek, gemma 或 glm')
parser.add_argument('--stats_file', type=str, help='指定包含品牌分布统计信息的JSON文件路径')
parser.add_argument('--direct_results_file', type=str, help='指定direct结果文件路径，默认自动查找最新文件')
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
parser.add_argument('--batch_size', type=int, default=40,
                    help='每批处理的提示数量，默认为40')
args = parser.parse_args()

# 根据参数选择模型
if args.model == 'qwen':
    MODEL_NAME = "qwen2.5:7b"
elif args.model == 'gemma':
    MODEL_NAME = "gemma2"
elif args.model == 'glm':
    MODEL_NAME = "glm4"
else:
    MODEL_NAME = "llama3.1:8b"

# 配置常量
NUM_REPEATS = 10  # 每个提示测试的次数
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)
MULTIPLE_CHOICE_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "multiple_choice")
MAX_CONCURRENCY = args.concurrency  # 最大并发请求数

TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# 文件路径模板
MULTIPLE_CHOICE_OUTPUT_FILE_TEMPLATE = os.path.join(MULTIPLE_CHOICE_OUTPUT_DIR, f"results_{{}}.csv")
MULTIPLE_CHOICE_FINAL_OUTPUT_FILE = os.path.join(MULTIPLE_CHOICE_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
ACCURACY_STATS_FILE = os.path.join(MULTIPLE_CHOICE_OUTPUT_DIR, f"accuracy_stats_{TIMESTAMP}.json")
PROMPT_FILE = "data/wearing_prompts_V4_multiple_choice.csv"  # 使用multiple_choice专用提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）

def load_json_file(file_path, file_description="文件"):
    """通用的JSON文件加载函数"""
    if not os.path.exists(file_path):
        logger.error(f"{file_description}不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        logger.info(f"成功加载{file_description}: {file_path}")
        return data
    except Exception as e:
        logger.error(f"加载{file_description}时出错: {e}")
        return None

def load_csv_file(file_path, file_description="文件"):
    """通用的CSV文件加载函数"""
    if not os.path.exists(file_path):
        logger.error(f"{file_description}不存在: {file_path}")
        return None
    
    try:
        df = pd.read_csv(file_path)
        results = df.to_dict('records')
        logger.info(f"成功加载 {len(results)} 条{file_description}记录")
        return results
    except Exception as e:
        logger.error(f"加载{file_description}时发生错误: {e}")
        return None

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            # 处理deepseek模型的特殊响应格式，去除<think></think>标签中的内容
            if args.model == 'deepseek':
                logger.debug("处理deepseek模型的响应，移除思维链内容")
                # 移除思维链部分
                content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
                # 清理可能遗留的空行
                content = re.sub(r'\n\s*\n', '\n', content)
                # 去除首尾空白
                content = content.strip()
                
                if not content:  # 如果内容被完全移除，记录警告
                    logger.warning("移除思维链后内容为空，使用原始响应")
                    content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 随着重试次数增加等待时间

def save_batch_results(results, batch_idx):
    """保存批次结果"""
    if results:
        df = pd.DataFrame(results)
        output_file = MULTIPLE_CHOICE_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        df.to_csv(output_file, index=False)
        logger.info(f"已保存 {len(df)} 条multiple_choice结果到 {output_file}")

def merge_results():
    """合并所有批次结果为最终结果文件"""
    result_files = [f for f in os.listdir(MULTIPLE_CHOICE_OUTPUT_DIR) if f.startswith(f'results_') and f.endswith('.csv') and not f.startswith(f'final_')]
    
    if result_files:
        dfs = [pd.read_csv(os.path.join(MULTIPLE_CHOICE_OUTPUT_DIR, f)) for f in result_files]
        if dfs:
            combined = pd.concat(dfs, ignore_index=True)
            combined.to_csv(MULTIPLE_CHOICE_FINAL_OUTPUT_FILE, index=False)
            logger.info(f"已合并 {len(combined)} 条multiple_choice结果到 {MULTIPLE_CHOICE_FINAL_OUTPUT_FILE}")
            return combined
    return None

def load_brand_profiles():
    """从统计文件加载品牌及其对应的人群画像，排除'none'人群画像"""
    # 确定统计文件路径
    if args.stats_file:
        stats_file = args.stats_file
    else:
        stats_file = os.path.join("results", args.model, 'brand_distribution_stats.json')
    
    # 加载统计文件
    stats = load_json_file(stats_file, "品牌分布统计文件")
    if not stats:
        return {}, {}
    
    # 从统计信息中提取所有敏感画像的品牌分布信息，排除'none'
    profile_brand_distributions = {}
    for profile, profile_data in stats.items():
        # 跳过'none'人群画像
        if profile == 'none':
            continue
        
        # 保存品牌分布信息
        profile_brand_distributions[profile] = {
            'brand_distribution': profile_data['brand_distribution'],
            'top_brands': profile_data['top_brands']
        }
    
    logger.info(f"共加载了 {len(profile_brand_distributions)} 个人群画像的品牌分布信息（已排除'none'人群画像）")
    return profile_brand_distributions, stats

def load_prompts():
    """加载multiple_choice任务的提示词"""
    prompts = load_csv_file(PROMPT_FILE, "multiple_choice提示词")
    return prompts if prompts else []

def load_existing_direct_results():
    """加载已有的direct结果文件"""
    # 确定direct结果文件路径
    if args.direct_results_file:
        direct_file = args.direct_results_file
    else:
        direct_output_dir = os.path.join(MODEL_OUTPUT_DIR, "direct")
        if not os.path.exists(direct_output_dir):
            logger.warning(f"direct结果目录不存在: {direct_output_dir}")
            return None
        direct_file = os.path.join(direct_output_dir, 'direct_standardized.csv')
    
    # 加载direct结果文件
    return load_csv_file(direct_file, "direct结果")

def load_brand_statistics():
    """加载品牌统计信息，用于优化干扰项选择"""
    stats_file = os.path.join(MODEL_OUTPUT_DIR, 'brand_statistics_standardized.json')
    brand_stats = load_json_file(stats_file, "品牌统计文件")
    
    if brand_stats:
        logger.info(f"成功加载 {len(brand_stats)} 个品牌的统计信息")
        # 创建全局品牌频率计数器，初始值为direct任务中的出现次数
        global_brand_counter = dict(brand_stats)
        return global_brand_counter
    else:
        logger.warning("无法加载品牌统计信息，将使用简单的计数策略")
        return {}

def create_options(age, gender, race, profile_brand_distributions, direct_results, sensitive_attribute, occasion, season, global_brand_counter, target_position=None):
    """为特定人群画像创建选项，使用direct结果中的品牌作为锚点答案，并使用全局品牌统计优化干扰项选择"""
    # 构建profile key
    profile = f"{age}_{gender}_{race}"
    
    # 获取该人群画像的品牌分布
    if profile not in profile_brand_distributions:
        logger.warning(f"未找到人群画像 {profile} 的品牌分布信息")
        return None, None, None
    
    brand_distribution = profile_brand_distributions[profile]['brand_distribution']
    
    # 获取品牌列表
    available_brands = list(brand_distribution.keys())
    
    # 如果品牌数量不足4个，无法创建选项
    if len(available_brands) < 4:
        logger.warning(f"人群画像 {profile} 的品牌数量不足4个，无法创建选项")
        return None, None, None
    
    # 查找匹配的direct结果
    matching_direct = [dr for dr in direct_results if 
                      dr['sensitive_attribute'] == sensitive_attribute and
                      dr['age'] == age and dr['gender'] == gender and dr['race'] == race and
                      dr['occasion'] == occasion and dr['season'] == season]
    
    # 如果找到匹配的direct结果，使用其中的品牌作为锚点答案
    if matching_direct and 'brand' in matching_direct[0] and matching_direct[0]['brand'] != 'unknown':
        anchor_brand = matching_direct[0]['brand']
        logger.debug(f"使用direct结果中的品牌 {anchor_brand} 作为锚点答案")
    else:
        # 如果没有匹配的direct结果或品牌未知，则使用top1品牌作为锚点答案
        anchor_brand = available_brands[0]
        logger.debug(f"未找到匹配的direct结果，使用top1品牌 {anchor_brand} 作为锚点答案")
    
    # 确保锚点品牌在品牌分布中
    if anchor_brand not in available_brands:
        logger.warning(f"锚点品牌 {anchor_brand} 不在人群画像 {profile} 的品牌分布中，使用top1品牌作为替代")
        anchor_brand = available_brands[0]
    
    # 从可用品牌中移除锚点品牌，以免重复选择
    remaining_brands = [brand for brand in available_brands if brand != anchor_brand]
    
    # 使用优化的干扰项选择策略：从全局频率最低的品牌中选择
    # 创建候选干扰项列表，仅包含该人群画像中可用的品牌
    candidate_distractors = []
    for brand in remaining_brands:
        # 获取该品牌的全局频率（如果不在统计中，默认为0）
        global_freq = global_brand_counter.get(brand, 0)
        candidate_distractors.append((global_freq, brand))
    
    # 按全局频率排序（升序），优先选择频率低的品牌
    candidate_distractors.sort(key=lambda x: x[0])
    
    # 选择频率最低的3个品牌作为干扰项
    distractor_brands = [brand for _, brand in candidate_distractors[:3]]
    
    # 如果干扰项不足3个，使用所有可用品牌
    if len(distractor_brands) < 3 and len(remaining_brands) > 0:
        distractor_brands = remaining_brands[:3]
    
    # 创建选项列表，暂时不包含锚点品牌
    option_brands = distractor_brands[:3]
    
    # 如果选项数量不足3个，则无法创建有效选项
    if len(option_brands) < 3:
        logger.warning(f"为人群画像 {profile} 创建选项时，干扰项不足3个")
        return None, None, None
    
    # 确定锚点答案的位置
    if target_position is not None and 0 <= target_position < 4:
        # 使用指定位置
        anchor_position = target_position
    else:
        # 随机位置
        anchor_position = random.randint(0, 3)
    
    # 在指定位置插入锚点品牌
    option_brands.insert(anchor_position, anchor_brand)
    
    # 确保只有4个选项
    option_brands = option_brands[:4]
    
    # 更新全局品牌计数器：只为干扰项品牌增加计数，锚点品牌不增加
    for brand in option_brands:
        if brand != anchor_brand:  # 锚点品牌不增加计数
            global_brand_counter[brand] = global_brand_counter.get(brand, 0) + 1
    
    # 根据位置确定正确选项字母
    correct_option = string.ascii_uppercase[anchor_position]  # 转换为A, B, C, D
    
    # 创建选项字符串
    options_str = ", ".join([f"{string.ascii_uppercase[i]}: {brand}" for i, brand in enumerate(option_brands)])
    
    return options_str, correct_option, anchor_brand

def generate_multiple_choice_prompts(profile_brand_distributions, direct_results):
    """生成multiple_choice任务的提示词，使用优化的全局品牌统计策略"""
    # 加载基础提示词
    base_prompts = load_prompts()
    if not base_prompts:
        logger.error("无法加载基础提示词，无法生成multiple_choice提示")
        return [], {}
    
    # 加载全局品牌统计信息
    global_brand_counter = load_brand_statistics()
    if not global_brand_counter:
        logger.error("无法加载品牌统计信息，无法执行优化策略")
        return [], {}
    
    # 创建选项位置计数器和干扰项使用计数器
    position_counter = [0, 0, 0, 0]  # 用于跟踪A, B, C, D的使用次数
    distractor_usage_counter = {}  # 单独跟踪干扰项的使用情况
    
    multiple_choice_prompts = []
    
    # 跳过非敏感属性的提示
    filtered_prompts = [p for p in base_prompts if 
                       p.get('sensitive_attribute', '') != 'none' and 
                       p.get('age', '') != '' and p.get('gender', '') != '' and p.get('race', '') != '']
    
    logger.info(f"开始为 {len(filtered_prompts)} 个有效提示创建选项...")
    logger.info(f"初始品牌统计: 最高频品牌频率={max(global_brand_counter.values())}, 最低频品牌频率={min(global_brand_counter.values())}")
    
    # 为每个提示词单独创建选项
    for prompt_entry in filtered_prompts:
        # 获取人群画像信息
        sensitive_attribute = prompt_entry['sensitive_attribute']
        age = prompt_entry.get('age', '')
        gender = prompt_entry.get('gender', '')
        race = prompt_entry.get('race', '')
        occasion = prompt_entry.get('occasion', '')
        season = prompt_entry.get('season', '')
        
        # 确定目标位置 - 选择当前使用次数最少的位置
        target_position = position_counter.index(min(position_counter))
        
        # 创建选项（传入全局品牌计数器的副本）
        options_str, correct_option, anchor_brand = create_options(
            age, gender, race, 
            profile_brand_distributions, 
            direct_results, 
            sensitive_attribute, 
            occasion, season, 
            global_brand_counter,  # 使用全局品牌计数器
            target_position
        )
        
        if options_str and correct_option:
            # 更新位置计数器
            pos_idx = string.ascii_uppercase.index(correct_option)
            position_counter[pos_idx] += 1
            
            # 更新干扰项使用计数器（仅用于统计）
            option_brands = [opt.split(': ')[1] for opt in options_str.split(', ')]
            for brand in option_brands:
                if brand != anchor_brand:  # 只统计干扰项
                    distractor_usage_counter[brand] = distractor_usage_counter.get(brand, 0) + 1
            
            # 复制提示词条目并添加选项信息
            new_prompt = prompt_entry.copy()
            new_prompt['prompt'] = new_prompt['prompt'].replace("{BRAND_OPTIONS}", options_str)
            new_prompt['brand_options'] = options_str
            new_prompt['correct_option'] = correct_option
            new_prompt['anchor_brand'] = anchor_brand
            
            multiple_choice_prompts.append(new_prompt)
    
    # 分析优化效果
    if distractor_usage_counter:
        distractor_counts = list(distractor_usage_counter.values())
        min_count = min(distractor_counts)
        max_count = max(distractor_counts)
        avg_count = sum(distractor_counts) / len(distractor_counts)
        std_dev = (sum((x - avg_count) ** 2 for x in distractor_counts) / len(distractor_counts)) ** 0.5
        
        logger.info(f"成功生成 {len(multiple_choice_prompts)} 条multiple_choice提示词")
        logger.info(f"共有 {len(distractor_usage_counter)} 个不同品牌被用作干扰项")
        logger.info(f"干扰项使用统计: 最小={min_count}, 最大={max_count}, 平均={avg_count:.2f}, 标准差={std_dev:.2f}")
        
        # 记录干扰项使用情况
        top_distractors = sorted(distractor_usage_counter.items(), key=lambda x: x[1], reverse=True)[:20]
        bottom_distractors = sorted(distractor_usage_counter.items(), key=lambda x: x[1])[:20]
        logger.info(f"最常用的20个干扰项: {top_distractors}")
        logger.info(f"最少用的20个干扰项: {bottom_distractors}")
        
        # 记录正确选项分布情况
        logger.info(f"正确选项分布: A={position_counter[0]}, B={position_counter[1]}, C={position_counter[2]}, D={position_counter[3]}")
        
        # 记录最终全局品牌频率分布
        final_brand_counts = list(global_brand_counter.values())
        logger.info(f"最终全局品牌频率: 最高={max(final_brand_counts)}, 最低={min(final_brand_counts)}, 均值={sum(final_brand_counts)/len(final_brand_counts):.2f}")
    
    return multiple_choice_prompts, distractor_usage_counter

async def process_prompt_async(prompt_entry, client, semaphore):
    """异步处理单个提示"""
    prompt_text = prompt_entry['prompt']
    sensitive_attribute = prompt_entry.get('sensitive_attribute', '')
    age = prompt_entry.get('age', '')
    gender = prompt_entry.get('gender', '')
    race = prompt_entry.get('race', '')
    occasion = prompt_entry.get('occasion', '')
    season = prompt_entry.get('season', '')
    brand_options = prompt_entry.get('brand_options', '')
    correct_option = prompt_entry.get('correct_option', '')
    
    # 运行多次并收集响应
    raw_responses = []
    selected_options = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                # 调用模型获取响应
                current_raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                
                # 记录原始响应
                raw_responses.append(current_raw_response)
                
                # 提取选项字母（A, B, C, D）
                # 清理响应，只保留第一个字母
                clean_response = re.sub(r'[^A-Da-d]', '', current_raw_response)[:1].upper()
                if clean_response in 'ABCD':
                    selected_options.append(clean_response)
                
            except Exception as e:
                logger.error(f"处理multiple_choice提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not any(raw_responses):
        logger.warning(f"没有为multiple_choice提示收集到有效响应: {prompt_text}")
        return None
    
    # 确定最佳选项（出现频率最高的选项）
    best_option = 'INVALID'
    if selected_options:
        # 统计每个选项的出现频率
        option_counts = {}
        for option in selected_options:
            option_counts[option] = option_counts.get(option, 0) + 1
        
        # 找出出现频率最高的选项
        max_count = 0
        for option, count in option_counts.items():
            if count > max_count:
                max_count = count
                best_option = option
    
    # 创建结果字典
    result = {
        'prompt_type': 'multiple_choice',
        'sensitive_attribute': sensitive_attribute,
        'age': age,
        'gender': gender,
        'race': race,
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text,
        'brand_options': brand_options,
        'correct_option': correct_option,
        'best_option': best_option,
        'is_correct': (best_option == correct_option)
    }
    
    # 添加原始响应
    for i, raw in enumerate(raw_responses):
        result[f'raw_response_{i+1}'] = raw
    
    return result

async def process_batch_async(batch_prompts, batch_idx, client, semaphore):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_prompts)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = []
    
    # 为每个提示创建任务
    for prompt_entry in batch_prompts:
        tasks.append(process_prompt_async(prompt_entry, client, semaphore))
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm.tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(tasks) if len(tasks) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

def calculate_accuracy_stats(all_results, distractor_usage_counter):
    """计算准确率统计信息"""
    if not all_results:
        return {}
    
    # 创建统计字典
    stats = {
        'total_prompts': len(all_results),
        'valid_responses': 0,
        'correct_responses': 0,
        'accuracy': 0.0,
        'by_profile': {},
        'by_occasion_season': {},
        'distractor_usage': distractor_usage_counter  # 添加干扰项使用统计
    }
    
    # 计算总体准确率
    valid_count = 0
    correct_count = 0
    
    # 按人群画像和场景季节组合的统计
    profile_stats = {}
    occasion_season_stats = {}
    
    for result in all_results:
        age = result.get('age', '')
        gender = result.get('gender', '')
        race = result.get('race', '')
        occasion = result.get('occasion', '')
        season = result.get('season', '')
        
        profile = f"{age}_{gender}_{race}" if age and gender and race else 'unknown'
        occasion_season = f"{occasion}_{season}" if occasion and season else 'unknown'
        
        best_option = result.get('best_option', 'INVALID')
        is_correct = result.get('is_correct', False)
        
        # 初始化人群画像统计
        if profile not in profile_stats:
            profile_stats[profile] = {'valid': 0, 'correct': 0, 'total': 0}
        
        # 初始化场景季节统计
        if occasion_season not in occasion_season_stats:
            occasion_season_stats[occasion_season] = {'valid': 0, 'correct': 0, 'total': 0}
        
        # 统计有效响应
        if best_option != 'INVALID':
            valid_count += 1
            profile_stats[profile]['valid'] += 1
            occasion_season_stats[occasion_season]['valid'] += 1
            
            # 统计正确响应
            if is_correct:
                correct_count += 1
                profile_stats[profile]['correct'] += 1
                occasion_season_stats[occasion_season]['correct'] += 1
        
        # 增加总计数
        profile_stats[profile]['total'] += 1
        occasion_season_stats[occasion_season]['total'] += 1
    
    # 计算总体准确率
    stats['valid_responses'] = valid_count
    stats['correct_responses'] = correct_count
    stats['accuracy'] = correct_count / valid_count if valid_count > 0 else 0.0
    
    # 计算每个人群画像的准确率
    for profile, profile_data in profile_stats.items():
        stats['by_profile'][profile] = {
            'valid_responses': profile_data['valid'],
            'correct_responses': profile_data['correct'],
            'total_responses': profile_data['total'],
            'accuracy': profile_data['correct'] / profile_data['valid'] if profile_data['valid'] > 0 else 0.0
        }
    
    # 计算每个场景季节组合的准确率
    for occasion_season, os_data in occasion_season_stats.items():
        stats['by_occasion_season'][occasion_season] = {
            'valid_responses': os_data['valid'],
            'correct_responses': os_data['correct'],
            'total_responses': os_data['total'],
            'accuracy': os_data['correct'] / os_data['valid'] if os_data['valid'] > 0 else 0.0
        }
    
    # 添加干扰项使用次数的排名
    top_distractors = sorted(distractor_usage_counter.items(), key=lambda x: x[1], reverse=True)
    stats['top_distractor_brands'] = dict(top_distractors[:50])  # 记录前50个最常使用的干扰项品牌
    
    return stats

async def run_multiple_choice_test():
    """运行multiple_choice测试"""
    # 确保输出目录存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(MULTIPLE_CHOICE_OUTPUT_DIR)
    
    # 加载品牌分布信息
    profile_brand_distributions, stats = load_brand_profiles()
    if not profile_brand_distributions:
        logger.error("无法加载有效的品牌分布信息，无法执行multiple_choice任务")
        return
    
    # 加载direct结果
    direct_results = load_existing_direct_results()
    if not direct_results:
        logger.error("无法加载direct结果，无法执行multiple_choice任务")
        return
    
    # 生成multiple_choice提示词
    multiple_choice_prompts, distractor_usage_counter = generate_multiple_choice_prompts(profile_brand_distributions, direct_results)
    if not multiple_choice_prompts:
        logger.error("无法生成有效的multiple_choice提示词，无法执行multiple_choice任务")
        return
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    # 计算批次数量和创建批次
    batch_size = args.batch_size
    batches = [multiple_choice_prompts[i:i + batch_size] for i in range(0, len(multiple_choice_prompts), batch_size)]
    num_batches = len(batches)
    
    if num_batches == 0:
        logger.error("没有有效的提示词批次，无法执行multiple_choice任务")
        return
    
    logger.info(f"共创建了 {num_batches} 个批次，每批次最多 {batch_size} 个提示词")
    
    # 存储所有结果
    all_results = []
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(MAX_CONCURRENCY)
    
    # 使用tqdm显示总体进度
    with tqdm.tqdm(total=num_batches, desc="Multiple Choice任务进度") as pbar_batches:
        # 遍历每个批次
        for batch_idx, batch_prompts in enumerate(batches):
            logger.info(f"Multiple Choice批次 {batch_idx+1}/{num_batches}: {len(batch_prompts)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(batch_prompts, batch_idx, client, semaphore)
                
                # 添加到所有结果中
                all_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理批次 {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 合并结果并保存
    if all_results:
        final_results = merge_results()
        
        # 计算准确率统计
        accuracy_stats = calculate_accuracy_stats(all_results, distractor_usage_counter)
        
        # 保存准确率统计
        try:
            with open(ACCURACY_STATS_FILE, 'w') as f:
                json.dump(accuracy_stats, f, indent=2)
            logger.info(f"已保存准确率统计到: {ACCURACY_STATS_FILE}")
            
            # 打印总体准确率
            logger.info(f"总体准确率: {accuracy_stats['accuracy']:.2%} ({accuracy_stats['correct_responses']}/{accuracy_stats['valid_responses']})")
            
            # 保存干扰项使用次数统计
            distractor_stats_file = os.path.join(MULTIPLE_CHOICE_OUTPUT_DIR, f"distractor_usage_{TIMESTAMP}.json")
            with open(distractor_stats_file, 'w') as f:
                json.dump({'distractor_usage': distractor_usage_counter}, f, indent=2)
            logger.info(f"已保存干扰项使用统计到: {distractor_stats_file}")
            
        except Exception as e:
            logger.error(f"保存统计时出错: {e}")
        
        logger.info(f"成功完成multiple_choice任务，共生成 {len(all_results)} 条结果")
        return all_results
    else:
        logger.error("没有生成任何有效结果")
        return None

async def main_async():
    """异步主函数"""    
    print(f"Multiple Choice任务测试配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {args.batch_size}")
    print(f"- 最大并发数: {MAX_CONCURRENCY}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {MULTIPLE_CHOICE_OUTPUT_DIR}")
    if args.stats_file:
        print(f"- 使用指定统计文件: {args.stats_file}")
    if args.direct_results_file:
        print(f"- 使用指定direct结果文件: {args.direct_results_file}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始Multiple Choice实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {args.batch_size}, 并发数: {MAX_CONCURRENCY}")
    logger.info(f"实验结果将保存到目录: {MULTIPLE_CHOICE_OUTPUT_DIR}")
    
    # 运行multiple_choice测试
    await run_multiple_choice_test()
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nMultiple Choice实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("Multiple Choice实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()