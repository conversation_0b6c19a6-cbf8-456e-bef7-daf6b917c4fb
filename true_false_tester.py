import pandas as pd
import os
import re
import time
import json
from tqdm import tqdm
import ollama
import random
import math
import asyncio
from datetime import datetime
import argparse
from log_config import setup_logger
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL

# 配置日志
logger = setup_logger("true_false_experiment.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='True/False任务测试程序')
parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                    help=get_models_help_text())
parser.add_argument('--direct_results_file', type=str, help='指定direct结果文件路径，默认自动查找最新文件')
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
args = parser.parse_args()

# 根据参数选择模型
MODEL_NAME = get_model_name(args.model)

# 配置常量
NUM_REPEATS = 10  # 每个提示测试的次数
BATCH_SIZE = 40  # 每批处理的提示数量
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)
MAX_CONCURRENCY = args.concurrency  # 最大并发请求数

# 按任务类型分类存储结果
TRUE_FALSE_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "true_false")

TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# 文件路径模板
TRUE_FALSE_OUTPUT_FILE_TEMPLATE = os.path.join(TRUE_FALSE_OUTPUT_DIR, f"results_{{}}.csv")
TRUE_FALSE_FINAL_OUTPUT_FILE = os.path.join(TRUE_FALSE_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
PROMPT_FILE = "data/wearing_prompts_V4_true_false.csv"  # 使用true_false专用提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def normalize_response(response):
    """标准化响应为Recommend或Not recommend"""
    # 清理文本：移除括号内容和多余空格
    text = re.sub(r'[\(\[\{][^\)\]\}]*[\)\]\}]', '', response.lower())
    cleaned = re.sub(r'\s+', ' ', text).strip()
    
    if "recommend" in cleaned and "not" not in cleaned:
        return "Recommend"
    elif any(phrase in cleaned for phrase in ["not recommend", "don't recommend", "do not recommend"]):
        return "Not recommend"
    elif any(word in cleaned for word in ["yes", "suitable", "appropriate", "good"]):
        return "Recommend"
    elif any(word in cleaned for word in ["no", "not", "unsuitable", "inappropriate", "bad"]):
        return "Not recommend"
    else:
        return "Unknown"

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 随着重试次数增加等待时间

def save_batch_results(true_false_results, batch_idx):
    """保存批次结果"""
    if true_false_results:
        df = pd.DataFrame(true_false_results)
        output_file = TRUE_FALSE_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        df.to_csv(output_file, index=False)
        logger.info(f"已保存 {len(df)} 条true_false结果到 {output_file}")

def load_existing_direct_results():
    """加载已有的direct结果文件"""
    # 确定direct结果文件路径
    if args.direct_results_file:
        direct_file = args.direct_results_file
    else:
        direct_output_dir = os.path.join(MODEL_OUTPUT_DIR, "direct")
        if not os.path.exists(direct_output_dir):
            logger.warning(f"direct结果目录不存在: {direct_output_dir}")
            return None
        direct_file = os.path.join(direct_output_dir, 'direct_standardized.csv')
    
    # 加载direct结果文件
    if not os.path.exists(direct_file):
        logger.error(f"direct结果不存在: {direct_file}")
        return None
    
    try:
        df = pd.read_csv(direct_file)
        results = df.to_dict('records')
        logger.info(f"成功加载 {len(results)} 条direct结果记录")
        return results
    except Exception as e:
        logger.error(f"加载direct结果时发生错误: {e}")
        return None

async def process_prompt_async(row, client, semaphore, brand):
    """异步处理单个提示"""
    prompt_text = row['prompt'].replace("{BRAND}", brand)
    sensitive_attribute = row['sensitive_attribute']
    age = row.get('age', '')
    gender = row.get('gender', '')
    race = row.get('race', '')
    occasion = row['occasion']
    season = row['season']
    
    # 运行多次并收集响应
    raw_responses = []
    normalized_responses = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                # 调用模型获取响应
                current_raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                
                # 标准化响应
                normalized_response = normalize_response(current_raw_response)
                
                # 记录结果
                raw_responses.append(current_raw_response)
                normalized_responses.append(normalized_response)
                
            except Exception as e:
                logger.error(f"处理true_false提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
                normalized_responses.append("Unknown")
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not any(raw_responses):
        logger.warning(f"没有为true_false提示收集到有效响应: {prompt_text}")
        return None
    
    # 确定最终结果（多数投票）
    recommend_count = normalized_responses.count("Recommend")
    not_recommend_count = normalized_responses.count("Not recommend")
    unknown_count = normalized_responses.count("Unknown")
    
    if recommend_count > not_recommend_count and recommend_count > unknown_count:
        final_result = "Recommend"
    elif not_recommend_count > recommend_count and not_recommend_count > unknown_count:
        final_result = "Not recommend"
    else:
        final_result = "Unknown"
    
    result = {
        'prompt_type': 'true_false',
        'sensitive_attribute': sensitive_attribute,
        'age': age,
        'gender': gender,
        'race': race,
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text,
        'brand': brand,
        'result': final_result
    }
    
    # 添加原始响应和标准化响应
    for i, (raw, norm) in enumerate(zip(raw_responses, normalized_responses)):
        result[f'raw_response_{i+1}'] = raw
        result[f'normalized_response_{i+1}'] = norm
    
    return result

async def process_batch_async(batch_df, batch_idx, client, semaphore, direct_results):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_df)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = []
    
    # 为每个提示找到对应的direct结果中的品牌
    for _, row in batch_df.iterrows():
        # 提取行数据
        age = row.get('age', '')
        gender = row.get('gender', '')
        race = row.get('race', '')
        sensitive_attribute = row['sensitive_attribute']
        occasion = row['occasion']
        season = row['season']
        
        # 查找匹配的direct结果
        matching_direct = [dr for dr in direct_results if 
                          dr['sensitive_attribute'] == sensitive_attribute and
                          dr['age'] == age and
                          dr['gender'] == gender and
                          dr['race'] == race and
                          dr['occasion'] == occasion and
                          dr['season'] == season]
        
        if matching_direct:
            # 使用匹配的direct结果中的品牌
            brand = matching_direct[0].get('brand', 'unknown')
            tasks.append(process_prompt_async(row, client, semaphore, brand))
        else:
            logger.warning(f"未找到匹配的direct结果: {age}_{gender}_{race}, {occasion}_{season}")
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(batch_df) if len(batch_df) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

async def process_true_false_prompts_async(batch_size=BATCH_SIZE):
    """异步处理true_false提示并保存结果"""
    # 确保基础输出目录和模型特定目录都存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(TRUE_FALSE_OUTPUT_DIR)
    
    # 读取提示文件
    try:
        prompts_df = pd.read_csv(PROMPT_FILE)
        logger.info(f"成功读取了 {len(prompts_df)} 个true_false提示")
    except Exception as e:
        logger.error(f"读取提示文件时发生错误: {e}")
        return
    
    # 加载direct结果
    direct_results = load_existing_direct_results()
    if not direct_results:
        logger.error("无法加载direct结果，无法执行true_false任务")
        return
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        # 使用generate方法替代chat方法进行测试
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    # 计算批次数量
    total_prompts = len(prompts_df)
    num_batches = math.ceil(total_prompts / batch_size)
    
    # 创建批次索引
    batch_indices = [(i, min(i + batch_size, total_prompts)) for i in range(0, total_prompts, batch_size)]
    
    # 存储所有结果
    all_results = []
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(MAX_CONCURRENCY)
    
    # 使用tqdm显示总体进度
    with tqdm(total=len(batch_indices), desc="True/False任务进度") as pbar_batches:
        # 遍历每个批次
        for batch_idx, (start_idx, end_idx) in enumerate(batch_indices):
            current_batch = prompts_df.iloc[start_idx:end_idx].copy()
            logger.info(f"True/False批次 {batch_idx+1}/{num_batches}: {len(current_batch)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(current_batch, batch_idx, client, semaphore, direct_results)
                
                # 添加到所有结果中
                all_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理批次 {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 直接保存所有累积的结果到最终文件
    if all_results:
        final_df = pd.DataFrame(all_results)
        final_df.to_csv(TRUE_FALSE_FINAL_OUTPUT_FILE, index=False)
        logger.info(f"已保存 {len(final_df)} 条true_false最终结果到 {TRUE_FALSE_FINAL_OUTPUT_FILE}")
        
        # 计算统计信息
        calculate_stats(all_results)
    
    return all_results

def calculate_stats(results):
    """计算并显示统计信息"""
    if not results:
        logger.warning("没有结果可供分析")
        return
    
    total = len(results)
    recommend_count = sum(1 for r in results if r['result'] == 'Recommend')
    not_recommend_count = sum(1 for r in results if r['result'] == 'Not recommend')
    unknown_count = sum(1 for r in results if r['result'] == 'Unknown')
    
    # 按敏感属性分组统计
    sensitive_stats = {}
    non_sensitive_stats = {}
    
    def update_stats(stats_dict, key, result_type):
        """统一的统计更新函数"""
        if key not in stats_dict:
            stats_dict[key] = {'total': 0, 'recommend': 0, 'not_recommend': 0, 'unknown': 0}
        stats_dict[key]['total'] += 1
        stats_dict[key][result_type.lower().replace(' ', '_')] += 1
    
    for result in results:
        result_type = result['result']
        
        # 敏感属性统计
        if result['sensitive_attribute'] != 'none':
            age = result.get('age', '')
            gender = result.get('gender', '')
            race = result.get('race', '')
            attr_value = f"{age}_{gender}_{race}" if age and gender and race else 'unknown'
            update_stats(sensitive_stats, attr_value, result_type)
        
        # 非敏感属性统计
        occasion = result.get('occasion', '')
        season = result.get('season', '')
        non_sensitive_value = f"{occasion}_{season}" if occasion and season else 'unknown'
        update_stats(non_sensitive_stats, non_sensitive_value, result_type)
    
    # 输出总体统计
    logger.info("=== True/False任务统计 ===")
    logger.info(f"总样本数: {total}")
    logger.info(f"推荐数量: {recommend_count} ({recommend_count/total:.2%})")
    logger.info(f"不推荐数量: {not_recommend_count} ({not_recommend_count/total:.2%})")
    logger.info(f"未知数量: {unknown_count} ({unknown_count/total:.2%})")
    
    # 保存统计结果到文件
    stats = {
        'total_samples': total,
        'recommend_count': recommend_count,
        'not_recommend_count': not_recommend_count,
        'unknown_count': unknown_count,
        'recommend_percentage': recommend_count/total if total > 0 else 0,
        'not_recommend_percentage': not_recommend_count/total if total > 0 else 0,
        'unknown_percentage': unknown_count/total if total > 0 else 0,
        'sensitive_stats': sensitive_stats,
        'non_sensitive_stats': non_sensitive_stats
    }
    
    stats_file = os.path.join(TRUE_FALSE_OUTPUT_DIR, f"stats_{TIMESTAMP}.json")
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    logger.info(f"统计结果已保存到 {stats_file}")

async def main_async():
    """异步主函数"""    
    print(f"True/False任务测试配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {BATCH_SIZE}")
    print(f"- 最大并发数: {MAX_CONCURRENCY}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {TRUE_FALSE_OUTPUT_DIR}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始True/False实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {BATCH_SIZE}, 并发数: {MAX_CONCURRENCY}")
    logger.info(f"实验结果将保存到目录: {TRUE_FALSE_OUTPUT_DIR}")
    
    # 处理提示
    await process_true_false_prompts_async(batch_size=BATCH_SIZE)
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nTrue/False实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("True/False实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main() 