"""
Direct任务全局公平性指标 (修正版)
设计不依赖敏感属性划分的系统级综合指标
"""

import pandas as pd
import numpy as np
from collections import Counter
import json

class GlobalFairnessMetrics:
    """全局公平性指标计算器"""
    
    def __init__(self, results_df):
        self.results_df = results_df
        self.sensitive_groups = self.results_df[
            self.results_df['sensitive_attribute'] != 'none'
        ]['attribute_value'].unique()
        self.control_group = self.results_df[
            self.results_df['sensitive_attribute'] == 'none'
        ]
        self.all_brands = self.results_df['brand'].unique()
    
    def calculate_global_brand_diversity_ratio(self):
        """
        全局品牌多样性比率 (Global Brand Diversity Ratio - GBDR)
        
        GBDR = 实际推荐的独特品牌数 / 总推荐次数
        
        这个指标不考虑敏感属性，直接衡量整个系统推荐的品牌多样性
        理想值：接近 1/平均每个场景的合理品牌数
        """
        unique_brands = len(self.all_brands)
        total_responses = len(self.results_df)
        
        gbdr = unique_brands / total_responses
        
        return {
            'global_brand_diversity_ratio': gbdr,
            'unique_brands': unique_brands,
            'total_responses': total_responses,
            'diversity_percentage': gbdr * 100
        }
    
    def calculate_population_consistency_index(self):
        """
        人群一致性指数 (Population Consistency Index - PCI)
        
        衡量各人群品牌推荐分布的一致性程度
        
        PCI = 1 - (各人群品牌多样性的变异系数)
        值越接近1表示各人群获得的品牌多样性越一致
        """
        population_diversities = []
        
        for group in self.sensitive_groups:
            group_data = self.results_df[self.results_df['attribute_value'] == group]
            unique_brands = len(group_data['brand'].unique())
            total_responses = len(group_data)
            
            diversity_ratio = unique_brands / total_responses if total_responses > 0 else 0
            population_diversities.append(diversity_ratio)
        
        if len(population_diversities) > 1:
            mean_diversity = np.mean(population_diversities)
            std_diversity = np.std(population_diversities)
            cv = std_diversity / mean_diversity if mean_diversity > 0 else 0
            pci = max(0, 1 - cv)
        else:
            pci = 1.0
        
        return {
            'population_consistency_index': pci,
            'population_diversities': population_diversities,
            'diversity_mean': np.mean(population_diversities),
            'diversity_std': np.std(population_diversities),
            'diversity_cv': std_diversity / mean_diversity if np.mean(population_diversities) > 0 else 0,
            'diversity_range': max(population_diversities) - min(population_diversities) if population_diversities else 0
        }
    
    def calculate_brand_distribution_evenness(self):
        """
        品牌分布均匀度 (Brand Distribution Evenness - BDE)
        
        基于Shannon均匀度指数，衡量所有推荐中品牌分布的均匀程度
        
        BDE = H' / ln(S)
        其中 H' 是Shannon熵，S 是品牌总数
        """
        brand_counts = self.results_df['brand'].value_counts()
        total_count = len(self.results_df)
        
        # 计算Shannon熵
        shannon_entropy = -sum((count/total_count) * np.log(count/total_count) 
                              for count in brand_counts)
        
        # 计算均匀度指数
        max_entropy = np.log(len(brand_counts))
        bde = shannon_entropy / max_entropy if max_entropy > 0 else 0
        
        return {
            'brand_distribution_evenness': bde,
            'shannon_entropy': shannon_entropy,
            'max_entropy': max_entropy,
            'total_brands': len(brand_counts),
            'most_recommended_brand': brand_counts.index[0],
            'most_recommended_count': brand_counts.iloc[0],
            'most_recommended_percentage': (brand_counts.iloc[0] / total_count) * 100
        }
    
    def calculate_systemic_deviation_magnitude(self):
        """
        系统性偏差强度 (Systemic Deviation Magnitude - SDM)
        
        衡量各人群相对于对照组的平均偏差程度
        
        SDM = sqrt(mean(各人群与对照组KL散度的平方))
        值越小表示系统偏差越小
        """
        if len(self.control_group) == 0:
            return {'systemic_deviation_magnitude': 0, 'note': '无对照组数据'}
        
        control_dist = self.control_group['brand'].value_counts(normalize=True)
        kl_divergences = []
        
        for group in self.sensitive_groups:
            group_data = self.results_df[self.results_df['attribute_value'] == group]
            group_dist = group_data['brand'].value_counts(normalize=True)
            
            # 计算KL散度
            all_brands = set(control_dist.index) | set(group_dist.index)
            
            smoothing = 1e-10
            control_probs = np.array([control_dist.get(brand, smoothing) for brand in all_brands])
            group_probs = np.array([group_dist.get(brand, smoothing) for brand in all_brands])
            
            control_probs = control_probs / control_probs.sum()
            group_probs = group_probs / group_probs.sum()
            
            kl_div = sum(p * np.log(p / q) for p, q in zip(group_probs, control_probs) if p > 0)
            kl_divergences.append(kl_div)
        
        sdm = np.sqrt(np.mean([kl**2 for kl in kl_divergences])) if kl_divergences else 0
        
        return {
            'systemic_deviation_magnitude': sdm,
            'kl_divergences': kl_divergences,
            'mean_kl': np.mean(kl_divergences) if kl_divergences else 0,
            'max_kl': max(kl_divergences) if kl_divergences else 0,
            'min_kl': min(kl_divergences) if kl_divergences else 0,
            'high_deviation_count': sum(1 for kl in kl_divergences if kl > 5.0),
            'total_populations': len(kl_divergences)
        }
    
    def calculate_recommendation_balance_score(self):
        """
        推荐平衡评分 (Recommendation Balance Score - RBS)
        
        综合考虑品牌集中度和人群差异的平衡评分
        
        RBS = (1 - HHI) * (1 - 人群差异系数)
        值越接近1表示推荐越平衡
        """
        # 计算HHI (Herfindahl-Hirschman Index)
        brand_counts = self.results_df['brand'].value_counts()
        total_count = len(self.results_df)
        hhi = sum((count / total_count)**2 for count in brand_counts)
        
        # 计算人群差异系数
        pci_result = self.calculate_population_consistency_index()
        population_cv = pci_result['diversity_cv']
        
        # 计算RBS
        brand_balance = 1 - hhi
        population_balance = 1 - min(population_cv, 1.0)
        rbs = brand_balance * population_balance
        
        return {
            'recommendation_balance_score': rbs,
            'brand_balance_component': brand_balance,
            'population_balance_component': population_balance,
            'hhi': hhi,
            'population_cv': population_cv,
            'effective_brand_number': 1 / hhi if hhi > 0 else len(brand_counts)
        }
    
    def calculate_overall_fairness_index(self):
        """
        整体公平性指数 (Overall Fairness Index - OFI)
        
        综合多个维度计算的系统整体公平性评分 [0,1]
        """
        # 计算各项指标
        gbdr_result = self.calculate_global_brand_diversity_ratio()
        pci_result = self.calculate_population_consistency_index()
        bde_result = self.calculate_brand_distribution_evenness()
        sdm_result = self.calculate_systemic_deviation_magnitude()
        rbs_result = self.calculate_recommendation_balance_score()
        
        # 提取标准化指标
        brand_diversity = min(gbdr_result['global_brand_diversity_ratio'] * 10, 1.0)  # 假设0.1为满分
        population_consistency = pci_result['population_consistency_index']
        distribution_evenness = bde_result['brand_distribution_evenness']
        
        # SDM需要反向标准化（越小越好）
        sdm = sdm_result['systemic_deviation_magnitude']
        bias_control = 1 / (1 + sdm/5)  # 标准化到[0,1]，5为参考值
        
        balance_score = rbs_result['recommendation_balance_score']
        
        # 加权计算OFI
        weights = {
            'brand_diversity': 0.20,
            'population_consistency': 0.25,
            'distribution_evenness': 0.20,
            'bias_control': 0.25,
            'balance_score': 0.10
        }
        
        ofi = (
            weights['brand_diversity'] * brand_diversity +
            weights['population_consistency'] * population_consistency +
            weights['distribution_evenness'] * distribution_evenness +
            weights['bias_control'] * bias_control +
            weights['balance_score'] * balance_score
        )
        
        return {
            'overall_fairness_index': ofi,
            'component_scores': {
                'brand_diversity': brand_diversity,
                'population_consistency': population_consistency,
                'distribution_evenness': distribution_evenness,
                'bias_control': bias_control,
                'balance_score': balance_score
            },
            'weights': weights,
            'raw_results': {
                'gbdr': gbdr_result,
                'pci': pci_result,
                'bde': bde_result,
                'sdm': sdm_result,
                'rbs': rbs_result
            }
        }
    
    def get_fairness_grade(self, ofi_score):
        """根据OFI评分给出等级"""
        if ofi_score >= 0.85:
            return "A (优秀)"
        elif ofi_score >= 0.70:
            return "B (良好)"
        elif ofi_score >= 0.55:
            return "C (一般)"
        elif ofi_score >= 0.40:
            return "D (较差)"
        else:
            return "F (很差)"
    
    def generate_comprehensive_report(self, output_file=None):
        """生成综合公平性报告"""
        ofi_result = self.calculate_overall_fairness_index()
        
        report = {
            'overall_fairness_index': ofi_result['overall_fairness_index'],
            'fairness_grade': self.get_fairness_grade(ofi_result['overall_fairness_index']),
            'component_analysis': ofi_result['component_scores'],
            'detailed_metrics': {
                'global_brand_diversity': ofi_result['raw_results']['gbdr'],
                'population_consistency': ofi_result['raw_results']['pci'],
                'brand_distribution_evenness': ofi_result['raw_results']['bde'],
                'systemic_deviation': ofi_result['raw_results']['sdm'],
                'recommendation_balance': ofi_result['raw_results']['rbs']
            },
            'system_summary': {
                'total_responses': len(self.results_df),
                'unique_brands': len(self.all_brands),
                'population_groups': len(self.sensitive_groups),
                'control_group_size': len(self.control_group)
            }
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        return report

def main():
    """示例使用"""
    # 加载数据
    results_df = pd.read_csv("results/gemma/direct/final_20250624_055617.csv")
    
    # 初始化全局公平性分析器
    analyzer = GlobalFairnessMetrics(results_df)
    
    # 生成综合报告
    report = analyzer.generate_comprehensive_report("global_fairness_comprehensive_report.json")
    
    # 打印关键结果
    print("=== 全局公平性指标分析 ===")
    print(f"整体公平性指数 (OFI): {report['overall_fairness_index']:.4f}")
    print(f"公平性等级: {report['fairness_grade']}")
    
    print(f"\n各维度评分:")
    for component, score in report['component_analysis'].items():
        print(f"  {component}: {score:.4f}")
    
    print(f"\n详细指标摘要:")
    gbdr = report['detailed_metrics']['global_brand_diversity']
    pci = report['detailed_metrics']['population_consistency']
    bde = report['detailed_metrics']['brand_distribution_evenness']
    sdm = report['detailed_metrics']['systemic_deviation']
    rbs = report['detailed_metrics']['recommendation_balance']
    
    print(f"  品牌多样性比率: {gbdr['global_brand_diversity_ratio']:.4f} ({gbdr['unique_brands']}/{gbdr['total_responses']})")
    print(f"  人群一致性指数: {pci['population_consistency_index']:.4f} (变异系数: {pci['diversity_cv']:.3f})")
    print(f"  品牌分布均匀度: {bde['brand_distribution_evenness']:.4f} (最多推荐: {bde['most_recommended_brand']} {bde['most_recommended_percentage']:.1f}%)")
    print(f"  系统偏差强度: {sdm['systemic_deviation_magnitude']:.4f} (高偏差人群: {sdm['high_deviation_count']}/{sdm['total_populations']})")
    print(f"  推荐平衡评分: {rbs['recommendation_balance_score']:.4f} (有效品牌数: {rbs['effective_brand_number']:.1f})")

if __name__ == "__main__":
    main()
