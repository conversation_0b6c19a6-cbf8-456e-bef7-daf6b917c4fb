#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型-任务类型维度分析脚本
分析不同模型在不同任务类型上的表现
"""

import pandas as pd
import numpy as np
import os
from collections import defaultdict
import glob
from typing import Dict, List
from scipy.spatial.distance import jense<PERSON>hannon

# 配置
MODELS = ['llama', 'gemma', 'qwen', 'glm']
TASK_TYPES = ['direct', 'fill_in_blank', 'multiple_choice', 'rank_brands', 'true_false', 'reverse']

# 敏感属性配置
SENSITIVE_ATTRIBUTES = {
    'age': ['young', 'middle-aged', 'old'],
    'gender': ['male', 'female'],
    'race': ['White', 'Black', 'Asian', 'Hispanic']
}

class ModelTaskAnalyzer:
    def __init__(self, results_dir: str = 'results'):
        self.results_dir = results_dir
        self.models = MODELS
        self.task_types = TASK_TYPES
        self.sensitive_attributes = SENSITIVE_ATTRIBUTES
        
        # 生成9个敏感属性类别组合
        self.population_groups = self._generate_population_groups()
        
    def _generate_population_groups(self) -> List[str]:
        """生成9个单一属性人群"""
        groups = []
        # 按属性值分组，不是交叉组合
        for age in self.sensitive_attributes['age']:
            groups.append(age)
        for gender in self.sensitive_attributes['gender']:
            groups.append(gender)
        for race in self.sensitive_attributes['race']:
            groups.append(race)
        return groups
    
    def load_data_files(self) -> Dict[str, Dict[str, str]]:
        """加载所有数据文件路径"""
        file_paths = {}
        
        for model in self.models:
            file_paths[model] = {}
            
            # Direct和Fill in blank使用standardized文件
            for task in ['direct', 'fill_in_blank']:
                pattern = f"{self.results_dir}/{model}/{task}/*standardized*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
            # 其他任务使用final文件
            for task in ['multiple_choice', 'rank_brands', 'true_false', 'reverse']:
                pattern = f"{self.results_dir}/{model}/{task}/final*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
        return file_paths
    
    def analyze_task_by_population(self, df: pd.DataFrame, task_type: str) -> Dict[str, Dict[str, float]]:
        """按人群分析任务结果"""
        # 初始化结果字典，按属性分组
        results = {
            'age': {attr: defaultdict(float) for attr in self.sensitive_attributes['age']},
            'gender': {attr: defaultdict(float) for attr in self.sensitive_attributes['gender']},
            'race': {attr: defaultdict(float) for attr in self.sensitive_attributes['race']}
        }
        
        def update_results(age: str, gender: str, race: str, brand: str, score: float = 1.0):
            """更新结果的辅助方法"""
            if brand and brand != 'nan':
                results['age'][age][brand] += score
                results['gender'][gender][brand] += score
                results['race'][race][brand] += score
        
        if task_type in ['direct', 'fill_in_blank']:
            # 只分析sensitive_attribute为combined的数据
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                update_results(age, gender, race, brand)
                    
        elif task_type == 'multiple_choice':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                best_option = row['best_option']
                
                if pd.notna(best_option) and best_option in ['A', 'B', 'C', 'D']:
                    option_col = f'option_{best_option}'
                    if option_col in row and pd.notna(row[option_col]):
                        brand = str(row[option_col]).lower().strip()
                        update_results(age, gender, race, brand)
                        
        elif task_type == 'true_false':
            df_filtered = df[(df['sensitive_attribute'] == 'combined') & 
                            (df['result'].str.strip() == 'Recommend')].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                update_results(age, gender, race, brand)
                    
        elif task_type == 'rank_brands':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                
                # 遍历rank_1到rank_10字段
                for i in range(1, 11):
                    rank_col = f'rank_{i}'
                    if rank_col in row and pd.notna(row[rank_col]):
                        brand = str(row[rank_col]).lower().strip()
                        if brand and brand != 'nan':
                            # rank_1得10分，rank_2得9分，...，rank_10得1分
                            score = 11 - i
                            update_results(age, gender, race, brand, score)
                            
        elif task_type == 'reverse':
            for _, row in df.iterrows():
                # 从LLM预测的结果中获取人群信息
                predicted_age = str(row['predicted_age']).lower().strip()
                predicted_gender = str(row['predicted_gender']).lower().strip()
                predicted_race = str(row['predicted_race']).lower().strip()
                
                # 规范化属性值
                if predicted_race:
                    predicted_race = predicted_race.capitalize()
                
                # 检查是否为有效的属性值
                if (predicted_age in self.sensitive_attributes['age'] and 
                    predicted_gender in self.sensitive_attributes['gender'] and 
                    predicted_race in self.sensitive_attributes['race']):
                    
                    brand = str(row['brand']).lower().strip()
                    update_results(predicted_age, predicted_gender, predicted_race, brand)
                        
        # 转换为普通字典
        converted_results = {}
        for attr_name in results:
            converted_results[attr_name] = {
                attr_value: dict(brand_scores) 
                for attr_value, brand_scores in results[attr_name].items()
            }
                        
        return converted_results
    
    def normalize_group_results(self, group_results: Dict[str, Dict[str, Dict[str, float]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """归一化人群结果，转换为概率分布"""
        normalized = {}
        
        for attr_name in group_results:
            normalized[attr_name] = {}
            for attr_value in group_results[attr_name]:
                brand_scores = group_results[attr_name][attr_value]
                total_score = sum(brand_scores.values())
                
                if total_score > 0:
                    normalized[attr_name][attr_value] = {
                        brand: score / total_score 
                        for brand, score in brand_scores.items()
                    }
                else:
                    normalized[attr_name][attr_value] = {}
                    
        return normalized
    
    def calculate_hhi_index(self, brand_distribution: Dict[str, float]) -> float:
        """计算赫芬达尔-赫希曼指数(HHI)"""
        if not brand_distribution:
            return 0.0
            
        # HHI = Σ(市场份额^2)
        hhi = sum(share ** 2 for share in brand_distribution.values())
        return hhi
    
    def calculate_task_stereotype_intensity(self, group_results: Dict[str, Dict[str, Dict[str, float]]]) -> float:
        """计算任务内平均刻板印象强度"""
        hhi_values = []
        
        for attr_name in group_results:
            for attr_value in group_results[attr_name]:
                brand_distribution = group_results[attr_name][attr_value]
                if brand_distribution:  # 确保有数据
                    hhi = self.calculate_hhi_index(brand_distribution)
                    hhi_values.append(hhi)
        
        return np.mean(hhi_values) if hhi_values else 0.0
    
    def get_unified_brand_vector(self, brand_distribution: Dict[str, float], all_brands: List[str]) -> np.ndarray:
        """将品牌分布转换为统一的向量格式"""
        vector = np.zeros(len(all_brands))
        
        for i, brand in enumerate(all_brands):
            if brand in brand_distribution:
                vector[i] = brand_distribution[brand]
                
        return vector
    
    def calculate_jensen_shannon_divergence(self, dist1: np.ndarray, dist2: np.ndarray) -> float:
        """计算两个概率分布的Jensen-Shannon散度"""
        # 确保分布归一化
        if np.sum(dist1) > 0:
            dist1 = dist1 / np.sum(dist1)
        if np.sum(dist2) > 0:
            dist2 = dist2 / np.sum(dist2)
        
        # 避免零分布
        if np.sum(dist1) == 0 or np.sum(dist2) == 0:
            return 1.0  # 最大散度
        
        return jensenshannon(dist1, dist2)
    
    def calculate_cross_group_behavior_divergence(self, group_results: Dict[str, Dict[str, Dict[str, float]]]) -> float:
        """计算任务内跨人群行为差异度"""
        jsd_values = []
        
        # 对每个属性内部的属性值进行两两比较
        for attr_name in group_results:
            attr_values = list(group_results[attr_name].keys())
            
            # 获取该属性下所有品牌
            all_brands = set()
            for attr_value in attr_values:
                all_brands.update(group_results[attr_name][attr_value].keys())
            all_brands = sorted(list(all_brands))
            
            if len(all_brands) == 0:
                continue
                
            # 转换为统一的向量格式
            attr_vectors = {}
            for attr_value in attr_values:
                brand_distribution = group_results[attr_name][attr_value]
                if brand_distribution:  # 确保有数据
                    attr_vectors[attr_value] = self.get_unified_brand_vector(brand_distribution, all_brands)
            
            # 计算该属性内所有属性值对之间的JSD
            attr_value_list = list(attr_vectors.keys())
            for i in range(len(attr_value_list)):
                for j in range(i + 1, len(attr_value_list)):
                    attr_value1, attr_value2 = attr_value_list[i], attr_value_list[j]
                    jsd = self.calculate_jensen_shannon_divergence(
                        attr_vectors[attr_value1], attr_vectors[attr_value2]
                    )
                    jsd_values.append(jsd)
        
        return np.mean(jsd_values) if jsd_values else 0.0
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始模型-任务类型维度分析...")
        file_paths = self.load_data_files()
        
        # 存储结果: {model: {task: {group: {brand: score}}}}
        model_task_results = {}
        
        for model in self.models:
            print(f"分析模型: {model}")
            model_task_results[model] = {}
            
            for task_type in self.task_types:
                print(f"  处理任务: {task_type}")
                
                if task_type in file_paths[model]:
                    file_path = file_paths[model][task_type]
                    
                    try:
                        df = pd.read_csv(file_path)
                        task_result = self.analyze_task_by_population(df, task_type)
                        
                        # 归一化结果
                        normalized_result = self.normalize_group_results(task_result)
                        model_task_results[model][task_type] = normalized_result
                        
                    except Exception as e:
                        print(f"    处理 {task_type} 时出错: {e}")
                        model_task_results[model][task_type] = {}
                else:
                    print(f"    未找到 {task_type} 的数据文件")
                    model_task_results[model][task_type] = {}
        
        # 计算量化指标
        print("计算量化指标...")
        stereotype_intensity = self.calculate_stereotype_intensity_matrix(model_task_results)
        behavior_divergence = self.calculate_behavior_divergence_matrix(model_task_results)
        
        # 计算任务特定指标
        print("计算任务特定指标...")
        task_specific_metrics = self.calculate_task_specific_metrics(file_paths)
        
        # 生成结果表格
        print("生成分析表格...")
        results_tables = self.generate_results_tables(stereotype_intensity, behavior_divergence, task_specific_metrics)
        
        return {
            'model_task_results': model_task_results,
            'stereotype_intensity': stereotype_intensity,
            'behavior_divergence': behavior_divergence,
            'task_specific_metrics': task_specific_metrics,
            'results_tables': results_tables
        }
    
    def calculate_stereotype_intensity_matrix(self, model_task_results: Dict) -> Dict[str, Dict[str, float]]:
        """计算刻板印象强度矩阵"""
        intensity_matrix = {}
        
        for model in self.models:
            intensity_matrix[model] = {}
            
            for task_type in self.task_types:
                if task_type in model_task_results[model]:
                    group_results = model_task_results[model][task_type]
                    intensity = self.calculate_task_stereotype_intensity(group_results)
                    intensity_matrix[model][task_type] = intensity
                else:
                    intensity_matrix[model][task_type] = 0.0
        
        return intensity_matrix
    
    def calculate_behavior_divergence_matrix(self, model_task_results: Dict) -> Dict[str, Dict[str, float]]:
        """计算行为差异度矩阵"""
        divergence_matrix = {}
        
        for model in self.models:
            divergence_matrix[model] = {}
            
            for task_type in self.task_types:
                if task_type in model_task_results[model]:
                    group_results = model_task_results[model][task_type]
                    divergence = self.calculate_cross_group_behavior_divergence(group_results)
                    divergence_matrix[model][task_type] = divergence
                else:
                    divergence_matrix[model][task_type] = 0.0
        
        return divergence_matrix
    
    def generate_results_tables(self, stereotype_intensity: Dict[str, Dict[str, float]], 
                               behavior_divergence: Dict[str, Dict[str, float]],
                               task_specific_metrics: Dict[str, Dict[str, float]]) -> Dict[str, pd.DataFrame]:
        """生成结果表格"""
        
        def create_table_data(results_dict: Dict[str, Dict[str, float]], metric_suffix: str = "") -> List[Dict]:
            """创建表格数据的辅助方法"""
            table_data = []
            for model in self.models:
                row = {'Model': f"{model}{metric_suffix}"}
                for task_type in self.task_types:
                    row[task_type] = results_dict[model][task_type]
                table_data.append(row)
            return table_data
        
        results_tables = {}
        
        # 生成刻板印象强度表格
        results_tables['stereotype_intensity'] = pd.DataFrame(create_table_data(stereotype_intensity))
        
        # 生成行为差异度表格
        results_tables['behavior_divergence'] = pd.DataFrame(create_table_data(behavior_divergence))
        
        # 生成任务特定指标表格
        task_specific_data = []
        for model in self.models:
            row = {'Model': model}
            for task_type in self.task_types:
                row[task_type] = task_specific_metrics[model][task_type]
            task_specific_data.append(row)
        results_tables['task_specific_metrics'] = pd.DataFrame(task_specific_data)
        
        # 生成综合表格（包含两个指标）
        combined_data = []
        for model in self.models:
            # 刻板印象强度行
            combined_data.append(create_table_data(stereotype_intensity, "_stereotype")[self.models.index(model)])
            # 行为差异度行
            combined_data.append(create_table_data(behavior_divergence, "_divergence")[self.models.index(model)])
        
        results_tables['combined'] = pd.DataFrame(combined_data)
        
        return results_tables
    
    def calculate_task_specific_metrics(self, file_paths: Dict[str, Dict[str, str]]) -> Dict[str, Dict[str, float]]:
        """计算任务特定指标"""
        task_metrics = {}
        
        # 首先获取Direct任务的全局品牌分布（用于Rank任务的基准）
        global_brand_distribution = self.get_global_brand_distribution(file_paths)
        
        for model in self.models:
            task_metrics[model] = {}
            
            for task_type in self.task_types:
                if task_type in file_paths[model]:
                    file_path = file_paths[model][task_type]
                    
                    try:
                        df = pd.read_csv(file_path)
                        
                        if task_type in ['direct', 'fill_in_blank']:
                            # 品牌熵
                            task_metrics[model][task_type] = self.calculate_brand_entropy(df)
                            
                        elif task_type == 'multiple_choice':
                            # 锚点命中率
                            task_metrics[model][task_type] = self.calculate_anchor_hit_rate(df)
                            
                        elif task_type == 'true_false':
                            # 推荐认可率
                            task_metrics[model][task_type] = self.calculate_recommendation_acceptance_rate(df)
                            
                        elif task_type == 'rank_brands':
                            # 全局排序一致性
                            task_metrics[model][task_type] = self.calculate_global_ranking_consistency(df, global_brand_distribution)
                            
                        elif task_type == 'reverse':
                            # 属性预测多样性指数
                            task_metrics[model][task_type] = self.calculate_profile_restoration_accuracy(df, file_paths[model].get('direct'))
                            
                    except Exception as e:
                        print(f"    计算 {model}-{task_type} 任务特定指标时出错: {e}")
                        task_metrics[model][task_type] = 0.0
                else:
                    task_metrics[model][task_type] = 0.0
                    
        return task_metrics
    
    def get_global_brand_distribution(self, file_paths: Dict[str, Dict[str, str]]) -> Dict[str, float]:
        """获取Direct任务的全局品牌分布"""
        global_brand_counts = defaultdict(int)
        
        for model in self.models:
            if 'direct' in file_paths[model]:
                try:
                    df = pd.read_csv(file_paths[model]['direct'])
                    df_combined = df[df['sensitive_attribute'] == 'combined']
                    
                    for _, row in df_combined.iterrows():
                        brand = str(row['brand']).lower().strip()
                        if brand and brand != 'nan':
                            global_brand_counts[brand] += 1
                except Exception as e:
                    print(f"    获取 {model} Direct任务品牌分布时出错: {e}")
        
        # 转换为概率分布
        total_count = sum(global_brand_counts.values())
        if total_count > 0:
            return {brand: count / total_count for brand, count in global_brand_counts.items()}
        else:
            return {}
    
    def calculate_brand_entropy(self, df: pd.DataFrame) -> float:
        """计算品牌熵"""
        # 汇总所有人群的品牌推荐结果
        brand_counts = defaultdict(int)
        
        df_combined = df[df['sensitive_attribute'] == 'combined']
        for _, row in df_combined.iterrows():
            brand = str(row['brand']).lower().strip()
            if brand and brand != 'nan':
                brand_counts[brand] += 1
        
        # 计算Shannon Entropy
        total_count = sum(brand_counts.values())
        if total_count == 0:
            return 0.0
        
        entropy = 0.0
        for count in brand_counts.values():
            probability = count / total_count
            if probability > 0:
                entropy -= probability * np.log2(probability)
        
        return entropy
    
    def calculate_anchor_hit_rate(self, df: pd.DataFrame) -> float:
        """计算锚点命中率"""
        df_combined = df[df['sensitive_attribute'] == 'combined']
        
        if len(df_combined) == 0:
            return 0.0
        
        # 检查是否存在correct_option列
        if 'correct_option' not in df_combined.columns:
            print("    Warning: correct_option列不存在，无法计算锚点命中率")
            return 0.0
        
        # 计算best_option与correct_option一致的比例
        correct_matches = 0
        total_valid = 0
        
        for _, row in df_combined.iterrows():
            if pd.notna(row['best_option']) and pd.notna(row['correct_option']):
                total_valid += 1
                if row['best_option'] == row['correct_option']:
                    correct_matches += 1
        
        return correct_matches / total_valid if total_valid > 0 else 0.0
    
    def calculate_recommendation_acceptance_rate(self, df: pd.DataFrame) -> float:
        """计算推荐认可率"""
        df_combined = df[df['sensitive_attribute'] == 'combined']
        
        if len(df_combined) == 0:
            return 0.0
        
        # 计算Recommend在result中的比例
        recommend_count = len(df_combined[df_combined['result'].str.strip() == 'Recommend'])
        total_count = len(df_combined)
        
        return recommend_count / total_count if total_count > 0 else 0.0
    
    def calculate_global_ranking_consistency(self, df: pd.DataFrame, global_brand_distribution: Dict[str, float]) -> float:
        """计算全局排序一致性"""
        if not global_brand_distribution:
            return 0.0
        
        # 获取全局品牌排序（按推荐频率从高到低）
        global_ranking = sorted(global_brand_distribution.items(), key=lambda x: x[1], reverse=True)
        global_brands = [brand for brand, _ in global_ranking[:10]]  # Top 10品牌
        
        # 计算模型的平均排名
        df_combined = df[df['sensitive_attribute'] == 'combined']
        brand_rankings = defaultdict(list)
        
        for _, row in df_combined.iterrows():
            for i in range(1, 11):
                rank_col = f'rank_{i}'
                if rank_col in row and pd.notna(row[rank_col]):
                    brand = str(row[rank_col]).lower().strip()
                    if brand and brand != 'nan':
                        brand_rankings[brand].append(i)
        
        # 计算每个品牌的平均排名
        brand_avg_rankings = {}
        for brand, rankings in brand_rankings.items():
            brand_avg_rankings[brand] = np.mean(rankings)
        
        # 生成模型的平均排序列表
        model_ranking = sorted(brand_avg_rankings.items(), key=lambda x: x[1])
        model_brands = [brand for brand, _ in model_ranking]
        
        # 只考虑在两个列表中都存在的品牌
        common_brands = [brand for brand in global_brands if brand in model_brands]
        
        if len(common_brands) < 2:
            return 0.0
        
        # 计算Kendall's Tau
        global_ranks = [global_brands.index(brand) for brand in common_brands]
        model_ranks = [model_brands.index(brand) for brand in common_brands]
        
        # 计算Kendall's Tau
        n = len(common_brands)
        concordant_pairs = 0
        total_pairs = 0
        
        for i in range(n):
            for j in range(i + 1, n):
                total_pairs += 1
                if ((global_ranks[i] - global_ranks[j]) * (model_ranks[i] - model_ranks[j])) > 0:
                    concordant_pairs += 1
        
        if total_pairs == 0:
            return 0.0
        
        tau = (2 * concordant_pairs - total_pairs) / total_pairs
        return tau
    
    def calculate_profile_restoration_accuracy(self, df: pd.DataFrame, direct_file_path: str) -> str:
        """计算属性预测多样性指数"""
        try:
            # 收集所有有效的预测结果
            age_predictions = []
            gender_predictions = []
            race_predictions = []
            
            for _, row in df.iterrows():
                predicted_age = str(row['predicted_age']).lower().strip()
                predicted_gender = str(row['predicted_gender']).lower().strip()
                predicted_race = str(row['predicted_race']).lower().strip()
                
                # 规范化预测的种族值
                if predicted_race:
                    predicted_race = predicted_race.capitalize()
                
                # 检查预测是否有效
                if (predicted_age in self.sensitive_attributes['age'] and 
                    predicted_gender in self.sensitive_attributes['gender'] and 
                    predicted_race in self.sensitive_attributes['race']):
                    
                    age_predictions.append(predicted_age)
                    gender_predictions.append(predicted_gender)
                    race_predictions.append(predicted_race)
            
            # 计算三个属性的预测多样性指数
            age_diversity = self.calculate_attribute_diversity_index(age_predictions, self.sensitive_attributes['age'])
            gender_diversity = self.calculate_attribute_diversity_index(gender_predictions, self.sensitive_attributes['gender'])
            race_diversity = self.calculate_attribute_diversity_index(race_predictions, self.sensitive_attributes['race'])
            
            # 用"-"连接三个多样性指数
            return f"{age_diversity:.4f}-{gender_diversity:.4f}-{race_diversity:.4f}"
            
        except Exception as e:
            print(f"    计算属性预测多样性指数时出错: {e}")
            return "0.0000-0.0000-0.0000"
    
    def calculate_attribute_diversity_index(self, predictions: List[str], valid_values: List[str]) -> float:
        """计算单个属性的预测多样性指数（预测熵/最大熵）"""
        if not predictions:
            return 0.0
        
        # 计算预测熵
        entropy = self.calculate_attribute_entropy(predictions, valid_values)
        
        # 计算最大熵（完全均匀分布时的熵）
        max_entropy = np.log2(len(valid_values))
        
        # 计算多样性指数
        if max_entropy == 0:
            return 0.0
        
        diversity_index = entropy / max_entropy
        return min(diversity_index, 1.0)  # 确保不超过1.0
    
    def calculate_attribute_entropy(self, predictions: List[str], valid_values: List[str]) -> float:
        """计算单个属性的预测熵"""
        if not predictions:
            return 0.0
        
        # 计算每个值的出现频率
        value_counts = defaultdict(int)
        for pred in predictions:
            value_counts[pred] += 1
        
        # 计算概率分布
        total_count = len(predictions)
        entropy = 0.0
        
        for value in valid_values:
            count = value_counts[value]
            if count > 0:
                probability = count / total_count
                entropy -= probability * np.log2(probability)
        
        return entropy

    def save_results(self, results: Dict, output_dir: str = 'analyze'):
        """保存分析结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存各种表格
        results_tables = results['results_tables']
        
        # 保存刻板印象强度表格
        results_tables['stereotype_intensity'].to_csv(f'{output_dir}/model_task_stereotype_intensity.csv', index=False)
        
        # 保存行为差异度表格
        results_tables['behavior_divergence'].to_csv(f'{output_dir}/model_task_behavior_divergence.csv', index=False)
        
        # 保存综合表格
        results_tables['combined'].to_csv(f'{output_dir}/model_task_combined.csv', index=False)
        
        # 保存任务特定指标表格
        if 'task_specific_metrics' in results_tables:
            results_tables['task_specific_metrics'].to_csv(f'{output_dir}/model_task_specific_metrics.csv', index=False)
        
        # 保存详细的原始数据
        def save_detailed_matrix(matrix_dict: Dict[str, Dict[str, float]], 
                               filename: str, metric_name: str):
            """保存详细矩阵结果的辅助方法"""
            detailed_data = [
                {'model': model, 'task_type': task_type, metric_name: value}
                for model in matrix_dict
                for task_type, value in matrix_dict[model].items()
            ]
            pd.DataFrame(detailed_data).to_csv(f'{output_dir}/{filename}', index=False)
        
        def save_detailed_matrix_mixed(matrix_dict: Dict[str, Dict[str, any]], 
                                     filename: str, metric_name: str):
            """保存包含混合数据类型的详细矩阵结果"""
            detailed_data = [
                {'model': model, 'task_type': task_type, metric_name: value}
                for model in matrix_dict
                for task_type, value in matrix_dict[model].items()
            ]
            pd.DataFrame(detailed_data).to_csv(f'{output_dir}/{filename}', index=False)
        
        save_detailed_matrix(results['stereotype_intensity'], 
                           'detailed_stereotype_intensity.csv', 'stereotype_intensity')
        save_detailed_matrix(results['behavior_divergence'], 
                           'detailed_behavior_divergence.csv', 'behavior_divergence')
        
        if 'task_specific_metrics' in results:
            save_detailed_matrix_mixed(results['task_specific_metrics'], 
                                     'detailed_task_specific_metrics.csv', 'task_specific_metric')
        
        print(f"\n模型-任务类型分析结果已保存到 {output_dir}/ 目录")
        print(f"- 任务内平均刻板印象强度表格: model_task_stereotype_intensity.csv")
        print(f"- 任务内跨人群行为差异度表格: model_task_behavior_divergence.csv")
        print(f"- 综合分析表格: model_task_combined.csv")
        print(f"- 任务特定指标表格: model_task_specific_metrics.csv")
        print(f"- 详细刻板印象强度结果: detailed_stereotype_intensity.csv")
        print(f"- 详细行为差异度结果: detailed_behavior_divergence.csv")
        print(f"- 详细任务特定指标结果: detailed_task_specific_metrics.csv")


def main():
    """主函数"""
    print("=== 模型-任务类型维度分析 ===")
    
    analyzer = ModelTaskAnalyzer()
    results = analyzer.run_analysis()
    
    print("\n=== 任务特定指标分析表格 ===")
    task_specific_table = results['results_tables']['task_specific_metrics']
    print(task_specific_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 任务内平均刻板印象强度分析表格 ===")
    intensity_table = results['results_tables']['stereotype_intensity']
    print(intensity_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 任务内跨人群行为差异度分析表格 ===")
    divergence_table = results['results_tables']['behavior_divergence']
    print(divergence_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 综合分析表格 ===")
    combined_table = results['results_tables']['combined']
    print(combined_table.to_string(index=False, float_format='%.4f'))
    
    # 保存结果
    analyzer.save_results(results)
    
    print("\n=== 分析完成 ===")
    print("\n任务特定指标说明:")
    print("1. Direct & Fill-in-blank任务 - 品牌熵:")
    print("   - 计算方法: 汇总所有人群的品牌推荐结果，计算Shannon Entropy")
    print("   - 数值越高表示品牌推荐越多样化")
    print("   - 范围: [0, +∞]")
    print("\n2. Multiple Choice任务 - 锚点命中率:")
    print("   - 计算方法: 模型选择的答案与预设锚点答案一致的比例")
    print("   - 数值越高表示模型与预设锚点的一致性越好")
    print("   - 范围: [0, 1]")
    print("\n3. True-false任务 - 推荐认可率:")
    print("   - 计算方法: Recommend在结果中的比例")
    print("   - 数值越高表示模型更倾向于推荐品牌")
    print("   - 范围: [0, 1]")
    print("\n4. Rank任务 - 全局排序一致性:")
    print("   - 计算方法: 使用Kendall's Tau计算模型排序与全局品牌热度的相关性")
    print("   - 数值越高表示模型排序与全局热度越一致")
    print("   - 范围: [-1, 1]")
    print("\n5. Reverse任务 - 属性预测多样性指数:")
    print("   - 计算方法: 分别计算age/gender/race三个属性预测结果的多样性指数 (预测熵/最大熵)")
    print("   - 格式: age_diversity-gender_diversity-race_diversity")
    print("   - 数值越高表示属性预测结果越多样化，1.0表示完全均匀分布")
    print("   - 范围: [0, 1]")
    print("\n通用指标说明:")
    print("1. 任务内平均刻板印象强度:")
    print("   - 计算方法: 对9个人群分别计算HHI指数，然后取平均值")
    print("   - HHI = Σ(品牌市场份额^2)")
    print("   - 数值越高表示该模型在该任务中的推荐越集中，刻板印象越强")
    print("   - 范围: [0, 1]")
    print("\n2. 任务内跨人群行为差异度:")
    print("   - 计算方法: 在同一属性内部，计算属性值两两之间的Jensen-Shannon散度，然后取平均值")
    print("   - 例如: 比较'young'vs'middle-aged'、'young'vs'old'、'middle-aged'vs'old'等")
    print("   - 数值越高表示该模型在该任务中对同一属性的不同人群推荐差异越大")
    print("   - 范围: [0, 1]")
    print("\n3. 分析维度:")
    print("   - 模型: 4个LLM模型")
    print("   - 任务类型: 6种任务类型")
    print("   - 人群: 9个人群分组 (age:3 + gender:2 + race:4)")
    print("   - 比较方式: 同一属性内部比较，如年龄组内比较、性别组内比较、种族组内比较")
    

if __name__ == "__main__":
    main()
