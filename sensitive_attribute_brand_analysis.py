#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感属性-品牌维度分析脚本
分析品牌在不同人群中的推荐偏好，计算SAI (Sensitive Attribute Index)
"""

import pandas as pd
import numpy as np
import os
import json
from collections import defaultdict, Counter
import glob
from typing import Dict, List, Tuple, Any

# 配置
MODELS = ['llama', 'gemma', 'qwen', 'glm']
TASK_TYPES = ['direct', 'fill_in_blank', 'multiple_choice', 'rank_brands', 'true_false', 'reverse']

# 敏感属性配置
SENSITIVE_ATTRIBUTES = {
    'age': ['young', 'middle-aged', 'old'],
    'gender': ['male', 'female'],
    'race': ['White', 'Black', 'Asian', 'Hispanic']
}

# 任务权重配置
TASK_WEIGHTS = {
    'direct': 0.2,
    'fill_in_blank': 0.15,
    'multiple_choice': 0.15,
    'rank_brands': 0.15,
    'true_false': 0.15,
    'reverse': 0.2
}

class SensitiveAttributeBrandAnalyzer:
    def __init__(self, results_dir: str = 'results'):
        self.results_dir = results_dir
        self.models = MODELS
        self.task_types = TASK_TYPES
        self.sensitive_attributes = SENSITIVE_ATTRIBUTES
        self.task_weights = TASK_WEIGHTS
        
        # 生成9个敏感属性类别
        self.attribute_groups = self.generate_attribute_groups()
        
    def generate_attribute_groups(self) -> Dict[str, List[str]]:
        """生成9个敏感属性类别"""
        groups = {}
        for attr_name, attr_values in self.sensitive_attributes.items():
            groups[attr_name] = attr_values
        return groups
    
    def load_data_files(self) -> Dict[str, Dict[str, str]]:
        """加载所有数据文件路径"""
        file_paths = {}
        
        for model in self.models:
            file_paths[model] = {}
            
            # Direct和Fill in blank使用standardized文件
            for task in ['direct', 'fill_in_blank']:
                pattern = f"{self.results_dir}/{model}/{task}/*standardized*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
            # 其他任务使用final文件
            for task in ['multiple_choice', 'rank_brands', 'true_false', 'reverse']:
                pattern = f"{self.results_dir}/{model}/{task}/final*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
        return file_paths
    
    def get_population_key(self, age: str, gender: str, race: str) -> str:
        """生成人群标识键"""
        return f"{age}_{gender}_{race}"
    
    def analyze_task_by_attribute(self, df: pd.DataFrame, task_type: str) -> Dict[str, Dict[str, Dict[str, float]]]:
        """按敏感属性分析任务结果"""
        results = {
            'age': defaultdict(lambda: defaultdict(float)),
            'gender': defaultdict(lambda: defaultdict(float)),
            'race': defaultdict(lambda: defaultdict(float))
        }
        
        if task_type in ['direct', 'fill_in_blank']:
            # 只分析sensitive_attribute为combined的数据
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                
                if brand and brand != 'nan':
                    results['age'][age][brand] += 1
                    results['gender'][gender][brand] += 1
                    results['race'][race][brand] += 1
                    
        elif task_type == 'multiple_choice':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                best_option = row['best_option']
                
                if pd.notna(best_option) and best_option in ['A', 'B', 'C', 'D']:
                    option_col = f'option_{best_option}'
                    if option_col in row and pd.notna(row[option_col]):
                        brand = str(row[option_col]).lower().strip()
                        results['age'][age][brand] += 1
                        results['gender'][gender][brand] += 1
                        results['race'][race][brand] += 1
                        
        elif task_type == 'true_false':
            df_filtered = df[(df['sensitive_attribute'] == 'combined') & 
                            (df['result'].str.strip() == 'Recommend')].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                brand = str(row['brand']).lower().strip()
                
                if brand and brand != 'nan':
                    results['age'][age][brand] += 1
                    results['gender'][gender][brand] += 1
                    results['race'][race][brand] += 1
                    
        elif task_type == 'rank_brands':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                age = row['age']
                gender = row['gender']
                race = row['race']
                
                # 遍历rank_1到rank_10字段
                for i in range(1, 11):
                    rank_col = f'rank_{i}'
                    if rank_col in row and pd.notna(row[rank_col]):
                        brand = str(row[rank_col]).lower().strip()
                        if brand and brand != 'nan':
                            # rank_1得10分，rank_2得9分，...，rank_10得1分
                            score = 11 - i
                            results['age'][age][brand] += score
                            results['gender'][gender][brand] += score
                            results['race'][race][brand] += score
                            
        elif task_type == 'reverse':
            for _, row in df.iterrows():
                # 从LLM预测的结果中获取人群信息
                predicted_age = str(row['predicted_age']).lower().strip()
                predicted_gender = str(row['predicted_gender']).lower().strip()
                predicted_race = str(row['predicted_race']).lower().strip()
                
                # 规范化属性值
                if predicted_race:
                    predicted_race = predicted_race.capitalize()
                
                # 检查是否为有效的属性值
                if (predicted_age in self.sensitive_attributes['age'] and 
                    predicted_gender in self.sensitive_attributes['gender'] and 
                    predicted_race in self.sensitive_attributes['race']):
                    
                    brand = str(row['brand']).lower().strip()
                    if brand and brand != 'nan':
                        results['age'][predicted_age][brand] += 1
                        results['gender'][predicted_gender][brand] += 1
                        results['race'][predicted_race][brand] += 1
                        
        # 转换为普通字典
        for attr_name in results:
            results[attr_name] = {k: dict(v) for k, v in results[attr_name].items()}
                        
        return results
    
    def normalize_attribute_results(self, attribute_results: Dict[str, Dict[str, Dict[str, float]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """归一化敏感属性结果"""
        normalized = {}
        
        for attr_name in attribute_results:
            normalized[attr_name] = {}
            for attr_value in attribute_results[attr_name]:
                brand_scores = attribute_results[attr_name][attr_value]
                total_score = sum(brand_scores.values())
                
                if total_score > 0:
                    normalized[attr_name][attr_value] = {
                        brand: score / total_score 
                        for brand, score in brand_scores.items()
                    }
                else:
                    normalized[attr_name][attr_value] = {}
                    
        return normalized
    
    def combine_attribute_results(self, normalized_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """合并任务结果"""
        combined = {}
        
        # 获取所有敏感属性、属性值和品牌
        all_attributes = set()
        all_attribute_values = {}
        all_brands = set()
        
        for task in normalized_results:
            for attr_name in normalized_results[task]:
                all_attributes.add(attr_name)
                if attr_name not in all_attribute_values:
                    all_attribute_values[attr_name] = set()
                
                for attr_value in normalized_results[task][attr_name]:
                    all_attribute_values[attr_name].add(attr_value)
                    for brand in normalized_results[task][attr_name][attr_value]:
                        all_brands.add(brand)
        
        # 为每个敏感属性合并任务结果
        for attr_name in all_attributes:
            combined[attr_name] = {}
            for attr_value in all_attribute_values[attr_name]:
                combined[attr_name][attr_value] = defaultdict(float)
                
                for task in self.task_types:
                    if (task in normalized_results and 
                        attr_name in normalized_results[task] and 
                        attr_value in normalized_results[task][attr_name]):
                        
                        task_weight = self.task_weights[task]
                        for brand, score in normalized_results[task][attr_name][attr_value].items():
                            combined[attr_name][attr_value][brand] += score * task_weight
                            
                # 转换为普通字典
                combined[attr_name][attr_value] = dict(combined[attr_name][attr_value])
                
        return combined
    
    def get_top_brands(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], top_n: int = 10) -> List[str]:
        """获取全局top品牌"""
        # 合并所有模型的结果
        global_brand_scores = defaultdict(float)
        
        for model in model_results:
            for attr_name in model_results[model]:
                for attr_value in model_results[model][attr_name]:
                    for brand, score in model_results[model][attr_name][attr_value].items():
                        global_brand_scores[brand] += score
        
        # 按得分排序，返回top N
        sorted_brands = sorted(global_brand_scores.items(), key=lambda x: x[1], reverse=True)
        return [brand for brand, score in sorted_brands[:top_n]]
    
    def calculate_sai(self, model_results: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], 
                     top_brands: List[str]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """计算SAI (Sensitive Attribute Index)"""
        
        # 1. 合并所有模型的结果（等权重）
        combined_results = {}
        
        for attr_name in ['age', 'gender', 'race']:
            combined_results[attr_name] = defaultdict(lambda: defaultdict(float))
            
            for model in model_results:
                if attr_name in model_results[model]:
                    for attr_value in model_results[model][attr_name]:
                        for brand, score in model_results[model][attr_name][attr_value].items():
                            combined_results[attr_name][attr_value][brand] += score / len(self.models)
        
        # 2. 计算每个品牌的全局平均偏好
        global_brand_avg = defaultdict(float)
        
        for brand in top_brands:
            total_score = 0
            count = 0
            
            for attr_name in combined_results:
                for attr_value in combined_results[attr_name]:
                    if brand in combined_results[attr_name][attr_value]:
                        total_score += combined_results[attr_name][attr_value][brand]
                        count += 1
            
            if count > 0:
                global_brand_avg[brand] = total_score / count
            else:
                global_brand_avg[brand] = 0.0
        
        # 3. 计算SAI
        sai_results = {}
        
        for attr_name in ['age', 'gender', 'race']:
            sai_results[attr_name] = {}
            
            for attr_value in self.sensitive_attributes[attr_name]:
                sai_results[attr_name][attr_value] = {}
                
                for brand in top_brands:
                    attr_score = combined_results[attr_name][attr_value].get(brand, 0.0)
                    global_avg = global_brand_avg[brand]
                    
                    # SAI = (特定人群中的偏好 - 全局平均偏好) / 全局平均偏好
                    if global_avg > 0:
                        sai = (attr_score - global_avg) / global_avg
                    else:
                        sai = 0.0
                    
                    sai_results[attr_name][attr_value][brand] = sai
        
        return sai_results
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始敏感属性-品牌维度分析...")
        file_paths = self.load_data_files()
        
        # 存储结果: {model: {attribute: {attribute_value: {brand: score}}}}
        model_results = {}
        
        for model in self.models:
            print(f"分析模型: {model}")
            model_results[model] = {}
            
            # 存储该模型的任务结果
            task_results = {}
            
            for task_type in self.task_types:
                if task_type in file_paths[model]:
                    file_path = file_paths[model][task_type]
                    
                    try:
                        df = pd.read_csv(file_path)
                        task_result = self.analyze_task_by_attribute(df, task_type)
                        task_results[task_type] = task_result
                        
                    except Exception as e:
                        print(f"    处理 {task_type} 时出错: {e}")
                        task_results[task_type] = {
                            'age': {},
                            'gender': {},
                            'race': {}
                        }
                else:
                    task_results[task_type] = {
                        'age': {},
                        'gender': {},
                        'race': {}
                    }
            
            # 归一化任务结果
            normalized_tasks = {}
            for task in task_results:
                normalized_tasks[task] = self.normalize_attribute_results(task_results[task])
            
            # 合并该模型的所有任务结果
            model_results[model] = self.combine_attribute_results(normalized_tasks)
        
        # 确定top 10品牌
        print("确定全局top 10品牌...")
        top_brands = self.get_top_brands(model_results, top_n=10)
        
        # 计算SAI
        print("计算SAI指标...")
        sai_results = self.calculate_sai(model_results, top_brands)
        
        # 生成结果表格
        print("生成分析表格...")
        results_tables = self.generate_results_tables(sai_results, top_brands)
        
        return {
            'model_results': model_results,
            'top_brands': top_brands,
            'sai_results': sai_results,
            'results_tables': results_tables
        }
    
    def generate_results_tables(self, sai_results: Dict[str, Dict[str, Dict[str, float]]], 
                               top_brands: List[str]) -> Dict[str, pd.DataFrame]:
        """生成结果表格"""
        
        results_tables = {}
        
        for attr_name in ['age', 'gender', 'race']:
            table_data = []
            
            for attr_value in self.sensitive_attributes[attr_name]:
                row = {attr_name.capitalize(): attr_value}
                
                # 添加每个品牌的SAI值
                for brand in top_brands:
                    sai_value = sai_results[attr_name][attr_value].get(brand, 0.0)
                    row[brand] = sai_value
                
                table_data.append(row)
            
            results_tables[attr_name] = pd.DataFrame(table_data)
        
        return results_tables
    
    def save_results(self, results: Dict, output_dir: str = 'analyze'):
        """保存分析结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存合并的敏感属性分类表格
        results_tables = results['results_tables']
        
        # 合并所有敏感属性表格到一个文件
        combined_data = []
        
        # 年龄数据
        age_table = results_tables['age']
        for _, row in age_table.iterrows():
            new_row = {'Attribute': 'age', 'Value': row['Age']}
            for brand in results['top_brands']:
                new_row[brand] = row[brand]
            combined_data.append(new_row)
        
        # 性别数据
        gender_table = results_tables['gender']
        for _, row in gender_table.iterrows():
            new_row = {'Attribute': 'gender', 'Value': row['Gender']}
            for brand in results['top_brands']:
                new_row[brand] = row[brand]
            combined_data.append(new_row)
        
        # 种族数据
        race_table = results_tables['race']
        for _, row in race_table.iterrows():
            new_row = {'Attribute': 'race', 'Value': row['Race']}
            for brand in results['top_brands']:
                new_row[brand] = row[brand]
            combined_data.append(new_row)
        
        # 保存合并后的表格
        combined_df = pd.DataFrame(combined_data)
        combined_df.to_csv(f'{output_dir}/sensitive_attribute_brand_analysis.csv', index=False)
        
        # 保存top品牌信息
        top_brands_df = pd.DataFrame({'brand': results['top_brands']})
        top_brands_df.to_csv(f'{output_dir}/top_brands.csv', index=False)
        
        # 保存详细的SAI结果
        sai_detailed = []
        for attr_name in results['sai_results']:
            for attr_value in results['sai_results'][attr_name]:
                for brand, sai_value in results['sai_results'][attr_name][attr_value].items():
                    sai_detailed.append({
                        'attribute': attr_name,
                        'attribute_value': attr_value,
                        'brand': brand,
                        'sai_value': sai_value
                    })
        
        sai_detailed_df = pd.DataFrame(sai_detailed)
        sai_detailed_df.to_csv(f'{output_dir}/sai_detailed_results.csv', index=False)
        
        print(f"\n敏感属性-品牌分析结果已保存到 {output_dir}/ 目录")
        print(f"- 敏感属性分类SAI表格: sensitive_attribute_brand_analysis.csv")
        print(f"- Top品牌列表: top_brands.csv")
        print(f"- SAI详细结果: sai_detailed_results.csv")


def main():
    """主函数"""
    print("=== 敏感属性-品牌维度分析 ===")
    
    analyzer = SensitiveAttributeBrandAnalyzer()
    results = analyzer.run_analysis()
    
    print("\n=== Top 10 品牌 ===")
    for i, brand in enumerate(results['top_brands'], 1):
        print(f"{i}. {brand}")
    
    print("\n=== SAI分析结果表格 ===")
    results_tables = results['results_tables']
    
    print("\n=== 按年龄分类的SAI表格 ===")
    age_table = results_tables['age']
    print(age_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 按性别分类的SAI表格 ===")
    gender_table = results_tables['gender']
    print(gender_table.to_string(index=False, float_format='%.4f'))
    
    print("\n=== 按种族分类的SAI表格 ===")
    race_table = results_tables['race']
    print(race_table.to_string(index=False, float_format='%.4f'))
    
    # 保存结果
    analyzer.save_results(results)
    
    print("\n=== 分析完成 ===")
    print("\nSAI指标说明:")
    print("- SAI = (特定人群中的品牌偏好 - 全局平均偏好) / 全局平均偏好")
    print("- SAI > 0: 该品牌在特定人群中偏好高于全局平均")
    print("- SAI < 0: 该品牌在特定人群中偏好低于全局平均")
    print("- SAI = 0: 该品牌在特定人群中偏好等于全局平均")
    

if __name__ == "__main__":
    main()
