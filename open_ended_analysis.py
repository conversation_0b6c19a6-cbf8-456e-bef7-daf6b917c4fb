#!/usr/bin/env python3
"""
开放式任务分析程序 - Direct 和 Fill-in-blank 任务比较分析
分析四个模型在两种开放式任务中的品牌多样性、一致率和重合率

版本优化说明：
- 直接使用标准化数据中的age、gender、race字段进行人群分类分析
- 避免从combined属性值中提取属性，提高效率和准确性
- 减少重复数据加载，优化程序性能
"""

import pandas as pd
import os
import json
from collections import Counter
from typing import Dict, List, Tuple, Set
import warnings
warnings.filterwarnings('ignore')

class OpenEndedAnalyzer:
    def __init__(self, results_dir: str = "results"):
        """
        初始化分析器
        
        Args:
            results_dir: 结果文件夹路径
        """
        self.results_dir = results_dir
        self.models = ["gemma", "glm", "llama", "qwen"]
        self.tasks = ["direct", "fill_in_blank"]
        
    def load_standardized_data(self, model: str, task: str) -> pd.DataFrame:
        """
        加载指定模型和任务的标准化数据
        
        Args:
            model: 模型名称
            task: 任务类型 (direct 或 fill_in_blank)
            
        Returns:
            过滤后的DataFrame (只包含 sensitive_attribute='combined' 的数据)
        """
        if task == "direct":
            file_path = os.path.join(self.results_dir, model, "direct", "direct_standardized.csv")
        else:  # fill_in_blank
            file_path = os.path.join(self.results_dir, model, "fill_in_blank", "fill_in_blank_standardized.csv")
            
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            return pd.DataFrame()
            
        df = pd.read_csv(file_path)
        
        # 只保留 sensitive_attribute 为 'combined' 的数据
        df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
        
        print(f"加载 {model} {task}: {len(df_filtered)} 条记录")
        return df_filtered
    
    def calculate_brand_diversity(self, df: pd.DataFrame) -> float:
        """
        计算品牌多样性：不同品牌数 / 总prompt数
        
        Args:
            df: 数据框
            
        Returns:
            品牌多样性比率
        """
        if len(df) == 0:
            return 0.0
            
        unique_brands = df['brand'].nunique()
        total_prompts = len(df)
        
        return unique_brands / total_prompts
    
    def get_brand_set(self, df: pd.DataFrame) -> Set[str]:
        """
        获取数据中所有唯一品牌的集合
        
        Args:
            df: 数据框
            
        Returns:
            品牌集合
        """
        return set(df['brand'].dropna().unique())
    
    def calculate_consistency_rate(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> float:
        """
        计算一致率：相同敏感属性及相同非敏感属性的提示在两个任务中推荐的品牌相同的频率
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            一致率
        """
        if len(direct_df) == 0 or len(fill_df) == 0:
            return 0.0
        
        # 创建匹配键：敏感属性值 + 非敏感属性值
        direct_df = direct_df.copy()
        fill_df = fill_df.copy()
        
        direct_df['match_key'] = direct_df['attribute_value'].astype(str) + "_" + direct_df['non_sensitive_value'].astype(str)
        fill_df['match_key'] = fill_df['attribute_value'].astype(str) + "_" + fill_df['non_sensitive_value'].astype(str)
        
        # 找到两个任务中都存在的匹配键
        direct_keys = set(direct_df['match_key'].unique())
        fill_keys = set(fill_df['match_key'].unique())
        common_keys = direct_keys.intersection(fill_keys)
        
        if len(common_keys) == 0:
            return 0.0
        
        consistent_count = 0
        total_comparable = 0
        
        for key in common_keys:
            direct_brands = direct_df[direct_df['match_key'] == key]['brand'].tolist()
            fill_brands = fill_df[fill_df['match_key'] == key]['brand'].tolist()
            
            # 对于每个键，比较所有可能的配对
            for d_brand in direct_brands:
                for f_brand in fill_brands:
                    total_comparable += 1
                    if d_brand == f_brand:
                        consistent_count += 1
        
        return consistent_count / total_comparable if total_comparable > 0 else 0.0
    
    def calculate_overlap_rate(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> float:
        """
        计算重合率：direct和fill_in_blank全局结果中都有的品牌频率
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            重合率
        """
        direct_brands = self.get_brand_set(direct_df)
        fill_brands = self.get_brand_set(fill_df)
        
        if len(direct_brands) == 0 or len(fill_brands) == 0:
            return 0.0
        
        # 计算交集
        overlap_brands = direct_brands.intersection(fill_brands)
        
        # 计算并集
        union_brands = direct_brands.union(fill_brands)
        
        return len(overlap_brands) / len(union_brands) if len(union_brands) > 0 else 0.0
    
    def get_top3_brands_with_ratio(self, df: pd.DataFrame) -> str:
        """
        获取top3品牌及其占比
        
        Args:
            df: 数据框
            
        Returns:
            格式化的top3品牌字符串，例如：brand1(0.25), brand2(0.20), brand3(0.15)
        """
        if len(df) == 0:
            return "无数据"
        
        # 计算品牌频次
        brand_counts = df['brand'].value_counts()
        total_count = len(df)
        
        # 获取top3
        top3 = brand_counts.head(3)
        
        # 格式化输出
        result_parts = []
        for brand, count in top3.items():
            ratio = count / total_count
            result_parts.append(f"{brand}({ratio:.3f})")
        
        return ", ".join(result_parts)

    def analyze_model(self, model: str) -> Dict:
        """
        分析单个模型的各项指标
        
        优化说明：减少重复数据加载，提高效率
        
        Args:
            model: 模型名称
            
        Returns:
            包含各项指标的字典
        """
        # 加载数据（只加载一次）
        direct_df = self.load_standardized_data(model, "direct")
        fill_df = self.load_standardized_data(model, "fill_in_blank")
        
        # 计算各项指标
        direct_diversity = self.calculate_brand_diversity(direct_df)
        fill_diversity = self.calculate_brand_diversity(fill_df)
        consistency_rate = self.calculate_consistency_rate(direct_df, fill_df)
        overlap_rate = self.calculate_overlap_rate(direct_df, fill_df)
        
        # 计算top3品牌
        direct_top3 = self.get_top3_brands_with_ratio(direct_df)
        fill_top3 = self.get_top3_brands_with_ratio(fill_df)
        
        # 计算人群分类分析（复用已加载的数据）
        demographic_results = self.analyze_demographic_groups_from_data(direct_df, fill_df)
        
        # 合并所有结果
        results = {
            'direct': direct_diversity,
            'fill_in_blank': fill_diversity,
            'consistency_rate': consistency_rate,
            'overlap_rate': overlap_rate,
            'direct_top3': direct_top3,
            'fill_in_blank_top3': fill_top3
        }
        
        # 添加人群分类结果
        results.update(demographic_results)
        
        return results
    
    def generate_analysis_table(self) -> pd.DataFrame:
        """
        生成分析结果表格
        
        Returns:
            分析结果的DataFrame
        """
        results = {}
        
        for model in self.models:
            print(f"\n分析模型: {model}")
            results[model] = self.analyze_model(model)
        
        # 转换为DataFrame
        df_results = pd.DataFrame.from_dict(results, orient='index')
        
        # 定义列的顺序
        base_cols = ['direct', 'fill_in_blank', 'consistency_rate', 'overlap_rate', 'direct_top3', 'fill_in_blank_top3']
        
        # 年龄分组列
        age_cols = ['age_young', 'age_middle-aged', 'age_old']
        
        # 性别分组列
        gender_cols = ['gender_male', 'gender_female']
        
        # 种族分组列
        race_cols = ['race_White', 'race_Black', 'race_Asian', 'race_Hispanic']
        
        # 所有列的顺序
        all_cols = base_cols + age_cols + gender_cols + race_cols
        
        # 重新排列列的顺序
        df_results = df_results[all_cols]
        
        # 重命名列
        column_names = ['Direct', 'Fill-in-blank', '一致率', '重合率', 'Direct Top3品牌', 'Fill-in-blank Top3品牌',
                       'Young人群', 'Middle-aged人群', 'Old人群',
                       'Male人群', 'Female人群',
                       'White人群', 'Black人群', 'Asian人群', 'Hispanic人群']
        
        df_results.columns = column_names
        
        return df_results
    
    def save_results(self, df_results: pd.DataFrame, output_file: str = "open_ended_analysis_results.csv"):
        """
        保存结果到文件
        
        Args:
            df_results: 结果DataFrame
            output_file: 输出文件名
        """
        df_results.to_csv(output_file)
        print(f"\n结果已保存到: {output_file}")
        
        # 同时保存为JSON格式
        json_file = output_file.replace('.csv', '.json')
        results_dict = df_results.to_dict('index')
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results_dict, f, indent=2, ensure_ascii=False)
        print(f"结果也已保存为JSON格式: {json_file}")
    
    def print_detailed_results(self, df_results: pd.DataFrame):
        """
        打印详细的结果分析
        
        Args:
            df_results: 结果DataFrame
        """
        print("\n" + "="*150)
        print("开放式任务分析结果")
        print("="*150)
        
        print("\n表格说明:")
        print("- Direct, Fill-in-blank: 品牌多样性 (不同品牌数/总prompt数)")
        print("- 一致率: 相同条件下两任务推荐相同品牌的频率")
        print("- 重合率: 两任务品牌集合的交集占并集的比例")
        print("- Top3品牌: 最常推荐的前三个品牌及其占比")
        print("- 人群分类: 各人群在Direct和Fill-in-blank任务中的top1品牌及占比")
        
        print(f"\n数值指标表格:")
        # 只显示数值列
        numeric_cols = ['Direct', 'Fill-in-blank', '一致率', '重合率']
        print(df_results[numeric_cols].round(4).to_string())
        
        print(f"\nTop3品牌分布:")
        print("-" * 150)
        for model in df_results.index:
            print(f"\n{model.upper()}:")
            print(f"  Direct Top3: {df_results.loc[model, 'Direct Top3品牌']}")
            print(f"  Fill-in-blank Top3: {df_results.loc[model, 'Fill-in-blank Top3品牌']}")
        
        print(f"\n人群分类分析:")
        print("-" * 150)
        
        # 年龄分组
        print(f"\n年龄分组分析:")
        age_cols = ['Young人群', 'Middle-aged人群', 'Old人群']
        for model in df_results.index:
            print(f"\n{model.upper()}:")
            for col in age_cols:
                print(f"  {col}: {df_results.loc[model, col]}")
        
        # 性别分组
        print(f"\n性别分组分析:")
        gender_cols = ['Male人群', 'Female人群']
        for model in df_results.index:
            print(f"\n{model.upper()}:")
            for col in gender_cols:
                print(f"  {col}: {df_results.loc[model, col]}")
        
        # 种族分组
        print(f"\n种族分组分析:")
        race_cols = ['White人群', 'Black人群', 'Asian人群', 'Hispanic人群']
        for model in df_results.index:
            print(f"\n{model.upper()}:")
            for col in race_cols:
                print(f"  {col}: {df_results.loc[model, col]}")
        
        print(f"\n统计摘要:")
        print(f"- 平均品牌多样性 (Direct): {df_results['Direct'].mean():.4f}")
        print(f"- 平均品牌多样性 (Fill-in-blank): {df_results['Fill-in-blank'].mean():.4f}")
        print(f"- 平均一致率: {df_results['一致率'].mean():.4f}")
        print(f"- 平均重合率: {df_results['重合率'].mean():.4f}")
        
        # 找出表现最好和最差的模型
        print(f"\n模型排名:")
        print(f"品牌多样性最高 (Direct): {df_results['Direct'].idxmax()} ({df_results['Direct'].max():.4f})")
        print(f"品牌多样性最高 (Fill-in-blank): {df_results['Fill-in-blank'].idxmax()} ({df_results['Fill-in-blank'].max():.4f})")
        print(f"一致率最高: {df_results['一致率'].idxmax()} ({df_results['一致率'].max():.4f})")
        print(f"重合率最高: {df_results['重合率'].idxmax()} ({df_results['重合率'].max():.4f})")
    
    def get_top1_brand_for_group(self, df: pd.DataFrame, attribute_type: str, group_value: str) -> Tuple[str, str]:
        """
        获取特定人群的Direct和Fill-in-blank任务的top1品牌及占比
        
        Args:
            df: 数据框
            attribute_type: 属性类型 ('age', 'gender', 'race')
            group_value: 组值 (如 'young', 'male', 'Asian')
            
        Returns:
            (direct_top1, fill_top1) 格式：'brand(ratio)'
        """
        # 直接使用对应的字段过滤特定人群
        group_df = df[df[attribute_type] == group_value]
        
        if len(group_df) == 0:
            return "无数据", "无数据"
        
        # 分别获取direct和fill_in_blank的数据
        direct_data = group_df[group_df['prompt_type'] == 'direct']
        fill_data = group_df[group_df['prompt_type'] == 'fill_in_blank']
        
        def get_top1_brand_str(data):
            if len(data) == 0:
                return "无数据"
            brand_counts = data['brand'].value_counts()
            if len(brand_counts) == 0:
                return "无数据"
            top_brand = brand_counts.index[0]
            top_count = brand_counts.iloc[0]
            ratio = top_count / len(data)
            return f"{top_brand}({ratio:.3f})"
        
        direct_top1 = get_top1_brand_str(direct_data)
        fill_top1 = get_top1_brand_str(fill_data)
        
        return direct_top1, fill_top1
    
    def analyze_demographic_groups(self, model: str) -> Dict[str, str]:
        """
        分析特定模型的人群分类结果
        
        优化说明：直接使用标准化数据中的age、gender、race字段，
        无需从combined属性值中提取
        
        Args:
            model: 模型名称
            
        Returns:
            包含各人群分类结果的字典
        """
        # 加载所有数据（包括direct和fill_in_blank）
        direct_df = self.load_standardized_data(model, "direct")
        fill_df = self.load_standardized_data(model, "fill_in_blank")
        
        # 合并数据，添加任务类型标识
        direct_df['prompt_type'] = 'direct'
        fill_df['prompt_type'] = 'fill_in_blank'
        combined_df = pd.concat([direct_df, fill_df], ignore_index=True)
        
        results = {}
        
        # 年龄分组 (3类) - 直接使用age字段
        age_groups = ['young', 'middle-aged', 'old']
        for age in age_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'age', age)
            results[f'age_{age}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        # 性别分组 (2类) - 直接使用gender字段
        gender_groups = ['male', 'female']
        for gender in gender_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'gender', gender)
            results[f'gender_{gender}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        # 种族分组 (4类) - 直接使用race字段
        race_groups = ['White', 'Black', 'Asian', 'Hispanic']
        for race in race_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'race', race)
            results[f'race_{race}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        return results

    def analyze_demographic_groups_from_data(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> Dict[str, str]:
        """
        从已加载的数据分析人群分类结果（优化版本，避免重复加载数据）
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            包含各人群分类结果的字典
        """
        # 合并数据，添加任务类型标识
        direct_df_copy = direct_df.copy()
        fill_df_copy = fill_df.copy()
        direct_df_copy['prompt_type'] = 'direct'
        fill_df_copy['prompt_type'] = 'fill_in_blank'
        combined_df = pd.concat([direct_df_copy, fill_df_copy], ignore_index=True)
        
        results = {}
        
        # 年龄分组 (3类) - 直接使用age字段
        age_groups = ['young', 'middle-aged', 'old']
        for age in age_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'age', age)
            results[f'age_{age}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        # 性别分组 (2类) - 直接使用gender字段
        gender_groups = ['male', 'female']
        for gender in gender_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'gender', gender)
            results[f'gender_{gender}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        # 种族分组 (4类) - 直接使用race字段
        race_groups = ['White', 'Black', 'Asian', 'Hispanic']
        for race in race_groups:
            direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, 'race', race)
            results[f'race_{race}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        return results

def main():
    """主函数"""
    print("开始开放式任务分析...")
    
    # 创建分析器
    analyzer = OpenEndedAnalyzer()
    
    # 生成分析结果
    results_df = analyzer.generate_analysis_table()
    
    # 打印详细结果
    analyzer.print_detailed_results(results_df)
    
    # 保存结果
    analyzer.save_results(results_df)
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()
