#!/usr/bin/env python3
"""
品牌提取器 - 从所有模型的Direct和Fill-in-blank任务中提取唯一品牌并排序
"""

import pandas as pd
import os
import json
import sys

# 导入模型配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_config import get_supported_model_choices


class BrandExtractor:
    def __init__(self, results_dir: str = "results"):
        """
        初始化品牌提取器
        
        Args:
            results_dir: 结果文件夹路径
        """
        self.results_dir = results_dir
        self.models = get_supported_model_choices()
        self.tasks = ["direct", "fill_in_blank"]
    
    def extract_all_brands(self) -> dict:
        """
        提取所有模型和任务中的唯一品牌，记录每个品牌出现的模型
        
        Returns:
            包含品牌和对应模型列表的字典
        """
        brand_models = {}  # {brand: set(models)}
        
        for model in self.models:
            for task in self.tasks:
                file_path = os.path.join(self.results_dir, model, task, f"{task}_standardized.csv")
                
                if os.path.exists(file_path):
                    try:
                        df = pd.read_csv(file_path)
                        # 过滤combined数据并提取品牌
                        combined_data = df[df['sensitive_attribute'] == 'combined']
                        brands = combined_data['brand'].dropna().unique()
                        
                        # 记录每个品牌出现的模型
                        for brand in brands:
                            if brand not in brand_models:
                                brand_models[brand] = set()
                            brand_models[brand].add(model)
                        
                        print(f"从 {model}/{task} 提取了 {len(brands)} 个品牌")
                    except Exception as e:
                        print(f"读取 {file_path} 时出错: {e}")
                else:
                    print(f"文件不存在: {file_path}")
        
        # 转换set为排序列表，并按品牌名排序
        result = {}
        for brand in sorted(brand_models.keys()):
            result[brand] = sorted(list(brand_models[brand]))
        
        print(f"\n总计提取到 {len(result)} 个唯一品牌")
        
        return result
    
    def save_brands(self, brand_data: dict, output_file: str = "brands_with_models.json"):
        """
        保存品牌及其对应模型到JSON文件
        
        Args:
            brand_data: 品牌和模型的映射字典
            output_file: 输出文件名
        """
        # 确保输出到analyze目录
        analyze_dir = "analyze"
        if not os.path.exists(analyze_dir):
            os.makedirs(analyze_dir)
        
        output_path = os.path.join(analyze_dir, output_file)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(brand_data, f, indent=2, ensure_ascii=False)
        
        print(f"品牌和模型映射已保存到: {output_path}")


def main():
    """主函数"""
    print("开始提取品牌...")
    
    # 创建提取器
    extractor = BrandExtractor()
    
    # 提取所有品牌及其模型信息
    brand_data = extractor.extract_all_brands()
    
    # 保存结果
    extractor.save_brands(brand_data)
    
    print("\n品牌提取完成!")


if __name__ == "__main__":
    main()
