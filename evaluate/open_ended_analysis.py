#!/usr/bin/env python3
"""
开放式任务分析程序 - Direct 和 Fill-in-blank 任务比较分析
分析四个模型在两种开放式任务中的品牌多样性、一致率和重合率

指标说明：
- Direct, Fill-in-blank: 品牌多样性 (不同品牌数/总prompt数)
- 一致率: 相同条件下两任务推荐相同品牌的频率
- 重合率: 两任务品牌集合的交集占并集的比例
- Top3品牌: 最常推荐的前三个品牌及其占比
- 人群分类: 各人群在Direct和Fill-in-blank任务中的top1品牌及占比

版本优化说明：
- 直接使用标准化数据中的age、gender、race字段进行人群分类分析
- 避免从combined属性值中提取属性，提高效率和准确性
- 减少重复数据加载，优化程序性能
"""

import pandas as pd
import os
import json
from typing import Dict, Tuple, Set
import warnings
warnings.filterwarnings('ignore')

# 导入模型配置
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_config import get_supported_model_choices

class OpenEndedAnalyzer:
    def __init__(self, results_dir: str = "results"):
        """
        初始化分析器
        
        Args:
            results_dir: 结果文件夹路径
        """
        self.results_dir = results_dir
        # 从模型配置文件获取支持的模型列表
        self.models = get_supported_model_choices()
        
    def get_available_models(self) -> list:
        """
        检测实际可用的模型（有数据文件的模型）
        
        Returns:
            可用模型列表
        """
        available_models = []
        
        for model in self.models:
            # 检查是否存在direct和fill_in_blank数据
            direct_path = os.path.join(self.results_dir, model, "direct", "direct_standardized.csv")
            fill_path = os.path.join(self.results_dir, model, "fill_in_blank", "fill_in_blank_standardized.csv")
            
            if os.path.exists(direct_path) and os.path.exists(fill_path):
                available_models.append(model)
            else:
                print(f"跳过模型 {model}: 缺少必要的数据文件")
        
        return available_models
        
    def load_standardized_data(self, model: str, task: str) -> pd.DataFrame:
        """
        加载指定模型和任务的标准化数据
        
        Args:
            model: 模型名称
            task: 任务类型 (direct 或 fill_in_blank)
            
        Returns:
            过滤后的DataFrame (只包含 sensitive_attribute='combined' 的数据)
        """
        file_path = os.path.join(self.results_dir, model, task, f"{task}_standardized.csv")
            
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            return pd.DataFrame()
            
        df = pd.read_csv(file_path)
        
        # 只保留 sensitive_attribute 为 'combined' 的数据
        df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
        
        print(f"加载 {model} {task}: {len(df_filtered)} 条记录")
        return df_filtered
    
    def calculate_brand_diversity(self, df: pd.DataFrame) -> float:
        """
        计算品牌多样性：不同品牌数 / 总prompt数
        
        Args:
            df: 数据框
            
        Returns:
            品牌多样性比率
        """
        if len(df) == 0:
            return 0.0
            
        unique_brands = df['brand'].nunique()
        total_prompts = len(df)
        
        return unique_brands / total_prompts
    
    def get_brand_set(self, df: pd.DataFrame) -> Set[str]:
        """
        获取数据中所有唯一品牌的集合
        
        Args:
            df: 数据框
            
        Returns:
            品牌集合
        """
        return set(df['brand'].dropna().unique())
    
    def calculate_consistency_rate(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> float:
        """
        计算一致率：相同敏感属性及相同非敏感属性的提示在两个任务中推荐的品牌相同的频率
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            一致率
        """
        if len(direct_df) == 0 or len(fill_df) == 0:
            return 0.0
        
        # 创建匹配键：敏感属性组合 + 非敏感属性组合
        direct_df = direct_df.copy()
        fill_df = fill_df.copy()
        
        # 敏感属性组合：age + gender + race
        direct_df['sensitive_key'] = direct_df['age'].astype(str) + "_" + direct_df['gender'].astype(str) + "_" + direct_df['race'].astype(str)
        fill_df['sensitive_key'] = fill_df['age'].astype(str) + "_" + fill_df['gender'].astype(str) + "_" + fill_df['race'].astype(str)
        
        # 非敏感属性组合：occasion + season
        direct_df['non_sensitive_key'] = direct_df['occasion'].astype(str) + "_" + direct_df['season'].astype(str)
        fill_df['non_sensitive_key'] = fill_df['occasion'].astype(str) + "_" + fill_df['season'].astype(str)
        
        # 完整匹配键
        direct_df['match_key'] = direct_df['sensitive_key'] + "_" + direct_df['non_sensitive_key']
        fill_df['match_key'] = fill_df['sensitive_key'] + "_" + fill_df['non_sensitive_key']
        
        # 找到两个任务中都存在的匹配键
        common_keys = set(direct_df['match_key'].unique()).intersection(set(fill_df['match_key'].unique()))
        
        if len(common_keys) == 0:
            return 0.0
        
        consistent_count = 0
        total_comparable = 0
        
        for key in common_keys:
            direct_brands = direct_df[direct_df['match_key'] == key]['brand'].tolist()
            fill_brands = fill_df[fill_df['match_key'] == key]['brand'].tolist()
            
            # 对于每个键，比较所有可能的配对
            for d_brand in direct_brands:
                for f_brand in fill_brands:
                    total_comparable += 1
                    if d_brand == f_brand:
                        consistent_count += 1
        
        return consistent_count / total_comparable if total_comparable > 0 else 0.0
    
    def calculate_overlap_rate(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> float:
        """
        计算重合率：direct和fill_in_blank全局结果中都有的品牌频率
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            重合率
        """
        direct_brands = self.get_brand_set(direct_df)
        fill_brands = self.get_brand_set(fill_df)
        
        if len(direct_brands) == 0 or len(fill_brands) == 0:
            return 0.0
        
        # 计算交集和并集
        overlap_brands = direct_brands.intersection(fill_brands)
        union_brands = direct_brands.union(fill_brands)
        
        return len(overlap_brands) / len(union_brands) if len(union_brands) > 0 else 0.0
    
    def get_top3_brands_with_ratio(self, df: pd.DataFrame) -> str:
        """
        获取top3品牌及其占比
        
        Args:
            df: 数据框
            
        Returns:
            格式化的top3品牌字符串，例如：brand1(0.25), brand2(0.20), brand3(0.15)
        """
        if len(df) == 0:
            return "无数据"
        
        # 计算品牌频次
        brand_counts = df['brand'].value_counts()
        total_count = len(df)
        
        # 获取top3
        top3 = brand_counts.head(3)
        
        # 格式化输出
        result_parts = []
        for brand, count in top3.items():
            ratio = count / total_count
            result_parts.append(f"{brand}({ratio:.3f})")
        
        return ", ".join(result_parts)

    def analyze_model(self, model: str) -> Dict:
        """
        分析单个模型的各项指标
        
        Args:
            model: 模型名称
            
        Returns:
            包含各项指标的字典
        """
        # 加载数据
        direct_df = self.load_standardized_data(model, "direct")
        fill_df = self.load_standardized_data(model, "fill_in_blank")
        
        # 计算各项指标
        results = {
            'direct': self.calculate_brand_diversity(direct_df),
            'fill_in_blank': self.calculate_brand_diversity(fill_df),
            'consistency_rate': self.calculate_consistency_rate(direct_df, fill_df),
            'overlap_rate': self.calculate_overlap_rate(direct_df, fill_df),
            'direct_top3': self.get_top3_brands_with_ratio(direct_df),
            'fill_in_blank_top3': self.get_top3_brands_with_ratio(fill_df)
        }
        
        # 添加人群分类结果
        results.update(self.analyze_demographic_groups_from_data(direct_df, fill_df))
        
        return results
    
    def generate_analysis_table(self) -> pd.DataFrame:
        """
        生成分析结果表格
        
        Returns:
            分析结果的DataFrame
        """
        # 获取实际可用的模型
        available_models = self.get_available_models()
        
        if not available_models:
            print("警告: 没有找到任何可用的模型数据")
            return pd.DataFrame()
        
        print(f"找到 {len(available_models)} 个可用模型: {', '.join(available_models)}")
        
        results = {}
        
        for model in available_models:
            print(f"\n分析模型: {model}")
            results[model] = self.analyze_model(model)
        
        # 转换为DataFrame
        df_results = pd.DataFrame.from_dict(results, orient='index')
        
        # 定义列的顺序和重命名
        column_mapping = {
            'direct': 'Direct',
            'fill_in_blank': 'Fill-in-blank',
            'consistency_rate': '一致率',
            'overlap_rate': '重合率',
            'direct_top3': 'Direct Top3品牌',
            'fill_in_blank_top3': 'Fill-in-blank Top3品牌',
            'age_young': 'Young人群',
            'age_middle-aged': 'Middle-aged人群',
            'age_old': 'Old人群',
            'gender_male': 'Male人群',
            'gender_female': 'Female人群',
            'race_White': 'White人群',
            'race_Black': 'Black人群',
            'race_Asian': 'Asian人群',
            'race_Hispanic': 'Hispanic人群'
        }
        
        # 重新排列列并重命名
        df_results = df_results[list(column_mapping.keys())].rename(columns=column_mapping)
        
        return df_results
    
    def save_results(self, df_results: pd.DataFrame, output_file: str = "open_ended_analysis_results.csv"):
        """
        保存结果到文件
        
        Args:
            df_results: 结果DataFrame
            output_file: 输出文件名
        """
        # 确保输出到analyze目录
        analyze_dir = "analyze"
        if not os.path.exists(analyze_dir):
            os.makedirs(analyze_dir)
        
        output_path = os.path.join(analyze_dir, output_file)
        df_results.to_csv(output_path)
        print(f"\n结果已保存到: {output_path}")
        
        # 同时保存为JSON格式
        json_file = output_file.replace('.csv', '.json')
        json_path = os.path.join(analyze_dir, json_file)
        results_dict = df_results.to_dict('index')
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results_dict, f, indent=2, ensure_ascii=False)
        print(f"结果也已保存为JSON格式: {json_path}")
    
    def get_top1_brand_for_group(self, df: pd.DataFrame, attribute_type: str, group_value: str) -> Tuple[str, str]:
        """
        获取特定人群的Direct和Fill-in-blank任务的top1品牌及占比
        
        Args:
            df: 数据框
            attribute_type: 属性类型 ('age', 'gender', 'race')
            group_value: 组值 (如 'young', 'male', 'Asian')
            
        Returns:
            (direct_top1, fill_top1) 格式：'brand(ratio)'
        """
        # 过滤特定人群
        group_df = df[df[attribute_type] == group_value]
        
        if len(group_df) == 0:
            return "无数据", "无数据"
        
        # 分别获取direct和fill_in_blank的数据
        direct_data = group_df[group_df['prompt_type'] == 'direct']
        fill_data = group_df[group_df['prompt_type'] == 'fill_in_blank']
        
        def get_top1_brand_str(data):
            if len(data) == 0:
                return "无数据"
            brand_counts = data['brand'].value_counts()
            if len(brand_counts) == 0:
                return "无数据"
            top_brand = brand_counts.index[0]
            top_count = brand_counts.iloc[0]
            ratio = top_count / len(data)
            return f"{top_brand}({ratio:.3f})"
        
        return get_top1_brand_str(direct_data), get_top1_brand_str(fill_data)
    
    def analyze_demographic_groups_from_data(self, direct_df: pd.DataFrame, fill_df: pd.DataFrame) -> Dict[str, str]:
        """
        从已加载的数据分析人群分类结果
        
        Args:
            direct_df: direct任务数据
            fill_df: fill_in_blank任务数据
            
        Returns:
            包含各人群分类结果的字典
        """
        # 合并数据，添加任务类型标识
        direct_df = direct_df.copy()
        fill_df = fill_df.copy()
        direct_df['prompt_type'] = 'direct'
        fill_df['prompt_type'] = 'fill_in_blank'
        combined_df = pd.concat([direct_df, fill_df], ignore_index=True)
        
        results = {}
        
        # 定义所有分组
        demographic_groups = {
            'age': ['young', 'middle-aged', 'old'],
            'gender': ['male', 'female'],
            'race': ['White', 'Black', 'Asian', 'Hispanic']
        }
        
        # 统一处理所有人群分组
        for attribute_type, groups in demographic_groups.items():
            for group_value in groups:
                direct_top1, fill_top1 = self.get_top1_brand_for_group(combined_df, attribute_type, group_value)
                results[f'{attribute_type}_{group_value}'] = f"Direct: {direct_top1} | Fill: {fill_top1}"
        
        return results

def main():
    """主函数"""
    print("开始开放式任务分析...")
    
    # 创建分析器
    analyzer = OpenEndedAnalyzer()
    
    # 生成分析结果
    results_df = analyzer.generate_analysis_table()
    
    # 保存结果
    analyzer.save_results(results_df)
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()
