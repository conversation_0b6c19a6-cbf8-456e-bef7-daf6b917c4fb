import pandas as pd
import os
import re
import time
import json
from tqdm import tqdm
import ollama
import random
import math
import asyncio
from datetime import datetime
import argparse
from log_config import setup_logger
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL

# 配置日志
logger = setup_logger("fill_in_blank_experiment.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='Fill-in-blank任务测试程序')
parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                    help=get_models_help_text())
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
args = parser.parse_args()

# 根据参数选择模型
MODEL_NAME = get_model_name(args.model)

# 配置常量
NUM_REPEATS = 10  # 每个提示测试的次数
BATCH_SIZE = 40  # 每批处理的提示数量
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)

# 按任务类型分类存储结果
FILL_BLANK_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "fill_in_blank")
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
FILL_BLANK_OUTPUT_FILE_TEMPLATE = os.path.join(FILL_BLANK_OUTPUT_DIR, f"results_{{}}.csv")
FILL_BLANK_FINAL_OUTPUT_FILE = os.path.join(FILL_BLANK_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
PROMPT_FILE = "data/wearing_prompts_V4_fill_in_blank.csv"  # 使用最新版本的提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def parse_fill_blank_response(response):
    """解析fill_in_blank任务的响应，提取品牌信息"""
    # 清理响应文本
    cleaned_text = response
    # 移除括号及其内容
    cleaned_text = re.sub(r'\([^)]*\)', '', cleaned_text)
    # 移除方括号及其内容
    cleaned_text = re.sub(r'\[[^\]]*\]', '', cleaned_text)
    # 移除大括号及其内容
    cleaned_text = re.sub(r'\{[^}]*\}', '', cleaned_text)
    # 移除多余空格
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    
    # 处理可能的前缀/后缀文本
    prefixes = [
        'the answer is', 'the brand is', 'the blank should be filled with',
        'the most appropriate brand would be', 'the suitable brand is',
        'the brand would be', 'brand:', 'answer:'
    ]
    
    for prefix in prefixes:
        if prefix in cleaned_text.lower():
            cleaned_text = re.sub(rf'(?i){prefix}\s*', '', cleaned_text)
    
    # 处理可能有明显分界符的情况，取第一部分
    for sep in [' - ', ' and ', ' with ', ' in ', ' for ', ' from ', ' or ', ',']:
        if sep in cleaned_text:
            cleaned_text = cleaned_text.split(sep)[0].strip()
    
    # 处理可能的引号
    cleaned_text = cleaned_text.strip('"\'')
    
    return cleaned_text

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法替代chat方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 使用异步等待

def select_best_response(responses):
    """从多次响应中选择最佳响应"""
    if not responses:
        return None
    
    if len(responses) == 1:
        return responses[0]
    
    # 对于fill_in_blank任务，基于响应频率选择最佳响应
    response_counts = {}
    for response in responses:
        response_lower = response.lower()
        response_counts[response_lower] = response_counts.get(response_lower, 0) + 1
    
    # 找出出现频率最高的响应
    max_count = max(response_counts.values())
    most_common_responses = [r for r, c in response_counts.items() if c == max_count]
    
    # 如果只有一个最高频率响应，直接返回
    if len(most_common_responses) == 1:
        # 返回原始大小写版本
        for resp in responses:
            if resp.lower() == most_common_responses[0]:
                return resp
        return most_common_responses[0]
    
    # 如果有多个相同频率的响应，返回第一个
    for resp in responses:
        if resp.lower() == most_common_responses[0]:
            return resp
    return responses[0]

def save_batch_results(fill_blank_results, batch_idx):
    """保存批次结果"""
    if fill_blank_results:
        df = pd.DataFrame(fill_blank_results)
        output_file = FILL_BLANK_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        df.to_csv(output_file, index=False)
        logger.info(f"已保存 {len(df)} 条fill_in_blank结果到 {output_file}")

async def process_prompt_async(row, client, semaphore):
    """异步处理单个提示"""
    prompt_text = row['prompt']
    sensitive_attribute = row['sensitive_attribute']
    age = row.get('age', '')
    gender = row.get('gender', '')
    race = row.get('race', '')
    occasion = row['occasion']
    season = row['season']
    
    # 运行多次并收集响应
    responses = []
    raw_responses = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                # 调用模型获取响应
                current_raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                
                # 解析品牌
                current_brand = parse_fill_blank_response(current_raw_response)
                
                # 记录结果
                raw_responses.append(current_raw_response)
                responses.append(current_brand)
                
            except Exception as e:
                logger.error(f"处理fill_in_blank提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not responses:
        logger.warning(f"没有为fill_in_blank提示收集到有效响应: {prompt_text}")
        return None
    
    # 选择最佳响应
    best_brand = select_best_response(responses)
    
    result = {
        'prompt_type': 'fill_in_blank',
        'sensitive_attribute': sensitive_attribute,
        'age': age,
        'gender': gender,
        'race': race,
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text,
        'brand': best_brand
    }
    
    # 添加原始响应
    for i, raw in enumerate(raw_responses):
        result[f'raw_response_{i+1}'] = raw
    
    return result

async def process_batch_async(batch_df, batch_idx, client, semaphore):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_df)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = [process_prompt_async(row, client, semaphore) for _, row in batch_df.iterrows()]
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(batch_df) if len(batch_df) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

async def process_fill_blank_prompts_async():
    """异步处理fill_in_blank提示并保存结果"""
    # 确保基础输出目录和模型特定目录都存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(FILL_BLANK_OUTPUT_DIR)
    
    # 读取提示文件
    try:
        prompts_df = pd.read_csv(PROMPT_FILE)
        logger.info(f"成功读取了 {len(prompts_df)} 个提示")
    except Exception as e:
        logger.error(f"读取提示文件时发生错误: {e}")
        return
    
    # 过滤出fill_in_blank提示
    fill_blank_prompts = prompts_df[prompts_df['prompt_type'] == 'fill_in_blank'].copy()
    logger.info(f"过滤出 {len(fill_blank_prompts)} 个fill_in_blank提示")
    
    if len(fill_blank_prompts) == 0:
        logger.warning("没有找到fill_in_blank提示，退出程序")
        return
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        # 使用generate方法替代chat方法进行测试
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    # 计算批次数量
    total_prompts = len(fill_blank_prompts)
    num_batches = math.ceil(total_prompts / BATCH_SIZE)
    
    # 创建批次索引
    batch_indices = [(i, min(i + BATCH_SIZE, total_prompts)) for i in range(0, total_prompts, BATCH_SIZE)]
    
    # 存储所有结果
    all_results = []
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(args.concurrency)
    
    # 使用tqdm显示总体进度
    with tqdm(total=len(batch_indices), desc="Fill-in-blank任务进度") as pbar_batches:
        # 遍历每个批次
        for batch_idx, (start_idx, end_idx) in enumerate(batch_indices):
            current_batch = fill_blank_prompts.iloc[start_idx:end_idx].copy()
            logger.info(f"批次 {batch_idx+1}/{num_batches}: {len(current_batch)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(current_batch, batch_idx, client, semaphore)
                
                # 添加到所有结果中
                all_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理批次 {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 直接保存所有结果到最终文件
    if all_results:
        final_df = pd.DataFrame(all_results)
        final_df.to_csv(FILL_BLANK_FINAL_OUTPUT_FILE, index=False)
        logger.info(f"已保存 {len(final_df)} 条fill_in_blank结果到 {FILL_BLANK_FINAL_OUTPUT_FILE}")
    else:
        logger.warning("没有收集到任何有效结果")

async def main_async():
    """异步主函数"""    
    print(f"Fill-in-blank任务测试配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {BATCH_SIZE}")
    print(f"- 最大并发数: {args.concurrency}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {FILL_BLANK_OUTPUT_DIR}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始fill_in_blank实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {BATCH_SIZE}, 并发数: {args.concurrency}")
    logger.info(f"实验结果将保存到目录: {FILL_BLANK_OUTPUT_DIR}")
    
    # 处理提示
    await process_fill_blank_prompts_async()
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nFill-in-blank实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("Fill-in-blank实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()