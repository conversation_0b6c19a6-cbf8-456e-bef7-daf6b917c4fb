import os
import logging

def setup_logger(log_filename):
    """
    设置日志记录器
    
    参数:
        log_filename: 日志文件名(不含路径)
    
    返回:
        配置好的logger对象
    """
    # 确保日志目录存在
    log_dir = "log"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{log_dir}/{log_filename}"),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger() 