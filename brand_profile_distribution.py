#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 生成品牌人群分布统计和反向分布
import pandas as pd
import os
import json
import logging
from collections import Counter
import argparse
from log_config import setup_logger
from model_config import get_supported_model_choices, get_models_help_text, DEFAULT_MODEL
# 不含为none的记录
# 配置日志
logger = setup_logger("brand_profile_analyzer.log")

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def load_direct_results(file_path=None, model_name=None):
    """
    加载direct结果文件
    
    参数:
        file_path: 直接指定文件路径
        model_name: 指定模型名称，自动查找该模型最新的direct结果
    
    返回:
        direct_results: 列表形式的direct结果
    """
    try:
        # 如果指定了文件路径，直接使用
        if file_path and os.path.exists(file_path):
            logger.info(f"使用指定的direct结果文件: {file_path}")
            df = pd.read_csv(file_path)
            results = df.to_dict('records')
            logger.info(f"成功加载 {len(results)} 条direct结果")
            return results
            
        # 如果指定了模型名称，查找该模型的结果目录
        if model_name:
            standardized_file = os.path.join("results", model_name, "direct", 'direct_standardized.csv')
            
            if os.path.exists(standardized_file):
                logger.info(f"加载标准化direct结果文件: {standardized_file}")
                df = pd.read_csv(standardized_file)
                results = df.to_dict('records')
                logger.info(f"成功加载 {len(results)} 条direct结果")
                return results
            else:
                logger.error(f"未找到模型 {model_name} 的标准化direct结果文件: {standardized_file}")
                
        logger.error("未能找到有效的direct结果文件")
        return None
    except Exception as e:
        logger.error(f"加载direct结果文件时发生错误: {e}")
        return None

def prepare_sensitive_data(direct_results):
    """
    准备敏感属性数据
    
    参数:
        direct_results: 列表形式的direct结果
        
    返回:
        sensitive_df: 包含敏感属性的DataFrame
    """
    if not direct_results:
        logger.error("direct结果为空")
        return None
        
    # 转换为DataFrame
    df = pd.DataFrame(direct_results)
    
    # 检查必要的列
    required_cols = ['sensitive_attribute', 'age', 'gender', 'race', 'brand']
    if not all(col in df.columns for col in required_cols):
        logger.error(f"direct结果缺少必要的列: {required_cols}")
        return None
    
    # 只处理敏感属性信息
    sensitive_df = df[df['sensitive_attribute'] == 'combined'].copy()
    
    if sensitive_df.empty:
        logger.warning("没有找到包含敏感属性信息的数据记录")
        return None
    
    # 创建敏感画像组合
    sensitive_df['profile'] = sensitive_df['age'].fillna('') + '_' + \
                           sensitive_df['gender'].fillna('') + '_' + \
                           sensitive_df['race'].fillna('')
    
    return sensitive_df

def analyze_brand_profiles(direct_results, top_n=1, output_dir=None):
    """
    按照敏感画像(age、gender、race组合)分别计算Top-N推荐品牌，
    并生成品牌分布统计信息。只处理sensitive_attribute为'combined'的记录。
    
    参数:
        direct_results: 列表形式的direct结果
        top_n: 每个敏感画像选择的Top-N品牌数量
        output_dir: 输出目录
    
    返回:
        stats: 详细的品牌分布统计信息，包含age、gender、race信息
    """
    # 准备敏感属性数据
    sensitive_df = prepare_sensitive_data(direct_results)
    if sensitive_df is None:
        return {}
    
    # 创建品牌分布统计
    stats = {}
    
    # 获取所有唯一的敏感画像组合
    unique_profiles = sensitive_df['profile'].unique()
    
    # 按照敏感画像分组
    for profile in unique_profiles:
        if profile.strip('_'):  # 跳过空的profile
            # 对于每个敏感画像，统计品牌频率
            profile_df = sensitive_df[sensitive_df['profile'] == profile]
            brand_counts = Counter(profile_df['brand'].tolist())
            
            # 根据频率排序品牌，频率相同时按字母序排序
            sorted_brands = sorted(brand_counts.items(), key=lambda x: (-x[1], x[0]))
            
            # 创建排序后的品牌分布字典
            sorted_brand_distribution = {brand: count for brand, count in sorted_brands}
            
            # 获取Top-N品牌（已经按频率和字母序排序）
            top_brands = [brand for brand, _ in sorted_brands[:top_n]]
            
            # 解析profile获得age, gender, race信息
            age, gender, race = profile.split('_')
            
            # 创建统计信息
            stats[profile] = {
                'age': age if age else None,
                'gender': gender if gender else None,
                'race': race if race else None,
                'total_count': len(profile_df),
                'brand_distribution': sorted_brand_distribution,
                'top_brands': top_brands
            }
    
    # 记录找到的敏感画像数量
    profile_count = len(stats)
    
    # 获取所有唯一品牌
    unique_top_brands = get_top_brands_from_stats(stats)
    
    logger.info(f"从{profile_count}个敏感画像中获取了{len(unique_top_brands)}个唯一Top-{top_n}品牌")
    
    # 如果指定了输出目录，保存结果
    if output_dir:
        save_json_file(stats, os.path.join(output_dir, "brand_distribution_stats.json"), "敏感画像品牌分布统计")
    
    return stats

def get_top_brands_from_stats(stats):
    """
    从品牌分布统计中提取所有唯一的Top品牌
    
    参数:
        stats: 品牌分布统计信息
    
    返回:
        unique_top_brands: 所有敏感画像的Top品牌合并去重后的列表
    """
    all_top_brands = []
    for profile_data in stats.values():
        all_top_brands.extend(profile_data['top_brands'])
    return list(set(all_top_brands))

def convert_to_brand_distribution(demographic_data):
    """
    将人群->品牌分布转换为品牌->人群分布
    
    参数:
        demographic_data: 人群分布数据 {demographic_id: {brand_distribution: {brand: count}}}
        
    返回:
        品牌分布数据 {brand: {demographics: {demographic_id: count}, total_count: int}}
    """
    brand_distribution = {}
    
    # 遍历所有人群
    for demographic_id, demographic_info in demographic_data.items():
        if 'brand_distribution' not in demographic_info:
            continue
            
        # 遍历该人群下的所有品牌
        for brand_name, count in demographic_info['brand_distribution'].items():
            # 使用字典的setdefault方法简化初始化
            if brand_name not in brand_distribution:
                brand_distribution[brand_name] = {
                    'demographics': {},
                    'total_count': 0
                }
            
            # 添加人群数据
            brand_distribution[brand_name]['demographics'][demographic_id] = count
            brand_distribution[brand_name]['total_count'] += count
    
    # 按数量降序排序每个品牌下的人群
    for brand_name in brand_distribution:
        brand_distribution[brand_name]['demographics'] = dict(sorted(
            brand_distribution[brand_name]['demographics'].items(), 
            key=lambda x: x[1], reverse=True
        ))
    
    # 按品牌总数量排序
    return dict(sorted(
        brand_distribution.items(), 
        key=lambda x: x[1]['total_count'], 
        reverse=True
    ))

def generate_brand_reverse_distribution(stats, output_dir):
    """
    生成品牌反向分布文件（品牌->人群分布）
    
    参数:
        stats: 品牌分布统计信息
        output_dir: 输出目录
    
    返回:
        brand_distribution: 品牌反向分布数据
    """
    if not stats:
        logger.warning("统计信息为空，无法生成品牌反向分布")
        return None
    
    # 转换数据格式
    brand_distribution = convert_to_brand_distribution(stats)
    
    # 保存反向分布文件
    reverse_file = os.path.join(output_dir, "brand_distribution_stats_reverse.json")
    if save_json_file(brand_distribution, reverse_file, "品牌反向分布"):
        # 输出统计信息
        total_brands = len(brand_distribution)
        total_demographic_entries = sum(len(brand_info['demographics']) 
                                      for brand_info in brand_distribution.values())
        
        logger.info(f"  - 品牌总数: {total_brands}")
        logger.info(f"  - 人群条目总数: {total_demographic_entries}")
        
        # 输出前5个品牌的示例
        logger.info("前5个品牌示例:")
        for i, (brand_name, brand_info) in enumerate(list(brand_distribution.items())[:5]):
            logger.info(f"  {i+1}. {brand_name}: {brand_info['total_count']}次提及, "
                       f"{len(brand_info['demographics'])}个人群")
        
        return brand_distribution
    
    return None

def save_json_file(data, file_path, description=""):
    """安全保存JSON文件的辅助函数"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"已保存{description}到: {file_path}")
        return True
    except Exception as e:
        logger.error(f"保存{description}时出错: {e}")
        return False

def format_demographic_info(profile_data):
    """格式化人口统计信息"""
    age = profile_data['age'] or '未知'
    gender = profile_data['gender'] or '未知' 
    race = profile_data['race'] or '未知'
    return f"{age}-{gender}-{race} (总计:{profile_data['total_count']}条记录)"

def process_brand_profiles(direct_results_file, model_name, top_n, output_dir):
    """处理品牌资料并生成报告"""
    # 加载direct结果
    direct_results = load_direct_results(direct_results_file, model_name)
    if not direct_results:
        logger.error("无法加载direct结果，程序退出")
        return
    
    # 分析品牌分布
    stats = analyze_brand_profiles(
        direct_results, 
        top_n=top_n, 
        output_dir=output_dir
    )
    
    # 获取所有唯一品牌
    unique_top_brands = get_top_brands_from_stats(stats)
    
    # 生成品牌反向分布
    brand_reverse_distribution = generate_brand_reverse_distribution(stats, output_dir)
    
    # 输出结果摘要
    print(f"\n敏感画像品牌分布分析完成!")
    print(f"- 分析模型: {model_name if model_name else '自定义文件'}")
    print(f"- Top-N数量: {top_n}")
    print(f"- 敏感画像数量: {len(stats)}")
    print(f"- 唯一品牌数量: {len(unique_top_brands)}")
    print(f"- 结果保存目录: {output_dir}")
    
    if brand_reverse_distribution:
        print(f"- 品牌反向分布: {len(brand_reverse_distribution)}个品牌")
    
    # 打印敏感画像详细信息
    print(f"\n敏感画像列表:")
    for i, (profile, data) in enumerate(stats.items(), 1):
        print(f"  {i}. {format_demographic_info(data)}")
    
    # 打印唯一品牌列表
    print("\n唯一Top品牌列表:")
    for i, brand in enumerate(sorted(unique_top_brands)):
        print(f"  {i+1}. {brand}")
    
    # 如果生成了反向分布，打印前几个品牌的信息
    if brand_reverse_distribution:
        print("\n品牌反向分布前5个品牌:")
        for i, (brand_name, brand_info) in enumerate(list(brand_reverse_distribution.items())[:5]):
            print(f"  {i+1}. {brand_name}: {brand_info['total_count']}次提及, "
                  f"{len(brand_info['demographics'])}个人群")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='敏感画像品牌分布统计工具')
    parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                        help=get_models_help_text())
    parser.add_argument('--direct_results_file', type=str, help='指定direct结果文件路径，优先级高于model参数')
    parser.add_argument('--top_n', type=int, default=1, help='每个敏感画像选择的Top-N品牌数量，默认为1')
    args = parser.parse_args()
    
    # 检查参数
    if not args.model and not args.direct_results_file:
        logger.error("必须指定--model或--direct_results_file参数")
        return
    
    # 设置输出目录
    output_dir = os.path.join("results", args.model) if args.model else "results"
    ensure_dir_exists(output_dir)
    
    # 处理品牌资料并生成报告
    process_brand_profiles(args.direct_results_file, args.model, args.top_n, output_dir)

if __name__ == "__main__":
    main() 