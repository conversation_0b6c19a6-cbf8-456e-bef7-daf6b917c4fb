#!/usr/bin/env python3
"""
批次结果清理工具
移除所有模型在所有任务下的批次结果文件 (results_*.csv)
"""

import os
import glob
from pathlib import Path


def cleanup_batch_results():
    """清理所有批次结果文件"""
    base_dir = Path(__file__).parent.parent / "results"
    
    if not base_dir.exists():
        print("Results目录不存在")
        return
    
    # 使用glob模式匹配所有批次结果文件
    pattern = str(base_dir / "**" / "results_*.csv")
    batch_files = glob.glob(pattern, recursive=True)
    
    if not batch_files:
        print("未找到批次结果文件")
        return
    
    # 删除文件
    deleted_count = 0
    for file_path in batch_files:
        try:
            os.remove(file_path)
            deleted_count += 1
        except OSError as e:
            print(f"删除失败: {file_path} - {e}")
    
    print(f"成功删除 {deleted_count} 个批次结果文件")


if __name__ == "__main__":
    cleanup_batch_results()
