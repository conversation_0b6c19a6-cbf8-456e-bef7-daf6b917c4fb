import os

import pandas as pd

DIRECT_OUTPUT_DIR = "results/internlm2/direct"


def merge_direct_results():
    """合并所有direct批次结果为最终direct结果文件"""
    # 修改文件匹配模式
    direct_files = [f for f in os.listdir(DIRECT_OUTPUT_DIR) if f.startswith(f'results_') and f.endswith('.csv') and not f.startswith(f'final_')]
    
    # 合并direct结果
    if direct_files:
        direct_dfs = [pd.read_csv(os.path.join(DIRECT_OUTPUT_DIR, f)) for f in direct_files]
        if direct_dfs:
            direct_combined = pd.concat(direct_dfs, ignore_index=True)
            direct_combined.to_csv(DIRECT_OUTPUT_DIR + "/final_direct_results.csv", index=False)
            # logger.info(f"已合并 {len(direct_combined)} 条direct结果到 {DIRECT_OUTPUT_DIR + '/final_direct_results.csv'}")
            return direct_combined
    return None
# 执行merge_direct_results
merge_direct_results()

