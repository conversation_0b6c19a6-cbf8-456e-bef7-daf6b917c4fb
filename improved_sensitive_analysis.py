#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感属性与模型选择组合分析脚本 - 改进版
独立分析每个敏感属性维度的影响
"""

import pandas as pd
import numpy as np
import os
import json
from collections import defaultdict, Counter
import glob
from typing import Dict, List, Tuple, Any

# 配置
MODELS = ['llama', 'gemma', 'qwen', 'glm']
TASK_TYPES = ['direct', 'fill_in_blank', 'multiple_choice', 'rank_brands', 'true_false', 'reverse']

# 敏感属性配置
SENSITIVE_ATTRIBUTES = {
    'age': ['young', 'middle-aged', 'old'],
    'gender': ['male', 'female'],
    'race': ['White', 'Black', 'Asian', 'Hispanic']
}

# 任务权重配置
TASK_WEIGHTS = {
    'direct': 0.2,
    'fill_in_blank': 0.15,
    'multiple_choice': 0.15,
    'rank_brands': 0.15,
    'true_false': 0.15,
    'reverse': 0.2
}

class ImprovedSensitiveAttributeAnalyzer:
    def __init__(self, results_dir: str = 'results'):
        self.results_dir = results_dir
        self.models = MODELS
        self.task_types = TASK_TYPES
        self.sensitive_attributes = SENSITIVE_ATTRIBUTES
        self.task_weights = TASK_WEIGHTS
        
    def load_data_files(self) -> Dict[str, Dict[str, str]]:
        """加载所有数据文件路径"""
        file_paths = {}
        
        for model in self.models:
            file_paths[model] = {}
            
            # Direct和Fill in blank使用standardized文件
            for task in ['direct', 'fill_in_blank']:
                pattern = f"{self.results_dir}/{model}/{task}/*standardized*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
            # 其他任务使用final文件
            for task in ['multiple_choice', 'rank_brands', 'true_false', 'reverse']:
                pattern = f"{self.results_dir}/{model}/{task}/final*.csv"
                files = glob.glob(pattern)
                if files:
                    file_paths[model][task] = files[0]
                    
        return file_paths
    
    def analyze_task_by_attribute(self, df: pd.DataFrame, task_type: str, attribute: str) -> Dict[str, Dict[str, float]]:
        """按特定敏感属性分析任务结果"""
        results = defaultdict(lambda: defaultdict(float))
        
        if task_type in ['direct', 'fill_in_blank']:
            # 只分析sensitive_attribute为combined的数据
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                attr_value = str(row[attribute])
                brand = str(row['brand']).lower().strip()
                if brand and brand != 'nan' and attr_value in self.sensitive_attributes[attribute]:
                    results[attr_value][brand] += 1
                    
        elif task_type == 'multiple_choice':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                attr_value = str(row[attribute])
                best_option = row['best_option']
                if pd.notna(best_option) and best_option in ['A', 'B', 'C', 'D'] and attr_value in self.sensitive_attributes[attribute]:
                    option_col = f'option_{best_option}'
                    if option_col in row and pd.notna(row[option_col]):
                        brand = str(row[option_col]).lower().strip()
                        results[attr_value][brand] += 1
                        
        elif task_type == 'true_false':
            df_filtered = df[(df['sensitive_attribute'] == 'combined') & 
                            (df['result'].str.strip() == 'Recommend')].copy()
            
            for _, row in df_filtered.iterrows():
                attr_value = str(row[attribute])
                brand = str(row['brand']).lower().strip()
                if brand and brand != 'nan' and attr_value in self.sensitive_attributes[attribute]:
                    results[attr_value][brand] += 1
                    
        elif task_type == 'rank_brands':
            df_filtered = df[df['sensitive_attribute'] == 'combined'].copy()
            
            for _, row in df_filtered.iterrows():
                attr_value = str(row[attribute])
                if attr_value in self.sensitive_attributes[attribute]:
                    # 遍历rank_1到rank_10字段
                    for i in range(1, 11):
                        rank_col = f'rank_{i}'
                        if rank_col in row and pd.notna(row[rank_col]):
                            brand = str(row[rank_col]).lower().strip()
                            if brand and brand != 'nan':
                                # rank_1得10分，rank_2得9分，...，rank_10得1分
                                score = 11 - i
                                results[attr_value][brand] += score
                                
        elif task_type == 'reverse':
            for _, row in df.iterrows():
                # 从LLM预测的结果中获取对应属性
                predicted_attr = str(row[f'predicted_{attribute}']).lower().strip()
                
                # 规范化属性值
                if attribute == 'race':
                    predicted_attr = predicted_attr.capitalize()
                
                if predicted_attr in self.sensitive_attributes[attribute]:
                    brand = str(row['brand']).lower().strip()
                    if brand and brand != 'nan':
                        results[predicted_attr][brand] += 1
                        
        return dict(results)
    
    def calculate_attribute_diversity(self, attr_results: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算特定属性的多样性指标"""
        diversity_scores = {}
        
        for attr_value in attr_results:
            brand_scores = attr_results[attr_value]
            
            if not brand_scores:
                diversity_scores[attr_value] = 0.0
                continue
            
            # 计算Shannon多样性指数
            total_score = sum(brand_scores.values())
            if total_score > 0:
                normalized_scores = [score / total_score for score in brand_scores.values()]
                shannon_diversity = -sum(p * np.log(p) for p in normalized_scores if p > 0)
                diversity_scores[attr_value] = shannon_diversity
            else:
                diversity_scores[attr_value] = 0.0
                
        return diversity_scores
    
    def calculate_attribute_hhi(self, attr_results: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算特定属性的Herfindahl-Hirschman Index (HHI)指标"""
        hhi_scores = {}
        
        for attr_value in attr_results:
            brand_scores = attr_results[attr_value]
            
            if not brand_scores:
                hhi_scores[attr_value] = 0.0
                continue
            
            # 计算HHI指数：各品牌市场份额平方和
            total_score = sum(brand_scores.values())
            if total_score > 0:
                market_shares = [score / total_score for score in brand_scores.values()]
                hhi = sum(share ** 2 for share in market_shares)
                hhi_scores[attr_value] = hhi
            else:
                hhi_scores[attr_value] = 0.0
                
        return hhi_scores
    
    def calculate_attribute_bias(self, attr_results: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算特定属性的偏见指标"""
        bias_scores = {}
        
        # 计算所有属性值的全局品牌分布
        global_brand_scores = defaultdict(float)
        total_attr_values = len(attr_results)
        
        for attr_value in attr_results:
            for brand, score in attr_results[attr_value].items():
                global_brand_scores[brand] += score / total_attr_values
        
        # 计算每个属性值与全局平均的KL散度
        all_brands = set(global_brand_scores.keys())
        
        for attr_value in attr_results:
            attr_scores = attr_results[attr_value]
            
            kl_divergence = 0.0
            total_attr_score = sum(attr_scores.values())
            total_global_score = sum(global_brand_scores.values())
            
            if total_attr_score > 0 and total_global_score > 0:
                for brand in all_brands:
                    p = attr_scores.get(brand, 0) / total_attr_score
                    q = global_brand_scores.get(brand, 1e-10) / total_global_score
                    
                    if p > 0:
                        kl_divergence += p * np.log(p / q)
            
            bias_scores[attr_value] = kl_divergence
            
        return bias_scores
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始敏感属性分析...")
        file_paths = self.load_data_files()
        
        # 存储结果: {model: {attribute: {task: {attr_value: {brand: score}}}}}
        analysis_results = {}
        
        for model in self.models:
            print(f"分析模型: {model}")
            analysis_results[model] = {}
            
            for attribute in self.sensitive_attributes.keys():
                print(f"  分析属性: {attribute}")
                analysis_results[model][attribute] = {}
                
                for task_type in self.task_types:
                    if task_type in file_paths[model]:
                        file_path = file_paths[model][task_type]
                        
                        try:
                            df = pd.read_csv(file_path)
                            task_results = self.analyze_task_by_attribute(df, task_type, attribute)
                            analysis_results[model][attribute][task_type] = task_results
                            
                        except Exception as e:
                            print(f"    处理 {task_type} 时出错: {e}")
                            analysis_results[model][attribute][task_type] = {}
                    else:
                        analysis_results[model][attribute][task_type] = {}
        
        # 归一化和合并结果
        print("归一化和合并任务结果...")
        normalized_results = self.normalize_results(analysis_results)
        combined_results = self.combine_task_results(normalized_results)
        
        # 计算指标
        print("计算多样性、偏见和HHI指标...")
        diversity_metrics = {}
        bias_metrics = {}
        hhi_metrics = {}
        
        for model in combined_results:
            diversity_metrics[model] = {}
            bias_metrics[model] = {}
            hhi_metrics[model] = {}
            
            for attribute in combined_results[model]:
                diversity_metrics[model][attribute] = self.calculate_attribute_diversity(combined_results[model][attribute])
                bias_metrics[model][attribute] = self.calculate_attribute_bias(combined_results[model][attribute])
                hhi_metrics[model][attribute] = self.calculate_attribute_hhi(combined_results[model][attribute])
        
        # 生成汇总表格
        print("生成汇总表格...")
        summary_table = self.generate_summary_table(diversity_metrics, bias_metrics, hhi_metrics)
        
        return {
            'analysis_results': analysis_results,
            'normalized_results': normalized_results,
            'combined_results': combined_results,
            'diversity_metrics': diversity_metrics,
            'bias_metrics': bias_metrics,
            'hhi_metrics': hhi_metrics,
            'summary_table': summary_table
        }
    
    def normalize_results(self, analysis_results):
        """归一化结果"""
        normalized = {}
        
        for model in analysis_results:
            normalized[model] = {}
            for attribute in analysis_results[model]:
                normalized[model][attribute] = {}
                for task in analysis_results[model][attribute]:
                    normalized[model][attribute][task] = {}
                    for attr_value in analysis_results[model][attribute][task]:
                        brand_scores = analysis_results[model][attribute][task][attr_value]
                        total_score = sum(brand_scores.values())
                        
                        if total_score > 0:
                            normalized[model][attribute][task][attr_value] = {
                                brand: score / total_score 
                                for brand, score in brand_scores.items()
                            }
                        else:
                            normalized[model][attribute][task][attr_value] = {}
                            
        return normalized
    
    def combine_task_results(self, normalized_results):
        """合并任务结果"""
        combined = {}
        
        for model in normalized_results:
            combined[model] = {}
            for attribute in normalized_results[model]:
                combined[model][attribute] = {}
                
                # 获取所有属性值和品牌
                all_attr_values = set()
                all_brands = set()
                
                for task in normalized_results[model][attribute]:
                    for attr_value in normalized_results[model][attribute][task]:
                        all_attr_values.add(attr_value)
                        for brand in normalized_results[model][attribute][task][attr_value]:
                            all_brands.add(brand)
                
                # 为每个属性值合并任务结果
                for attr_value in all_attr_values:
                    combined[model][attribute][attr_value] = defaultdict(float)
                    
                    for task in self.task_types:
                        if task in normalized_results[model][attribute] and \
                           attr_value in normalized_results[model][attribute][task]:
                            task_weight = self.task_weights[task]
                            for brand, score in normalized_results[model][attribute][task][attr_value].items():
                                combined[model][attribute][attr_value][brand] += score * task_weight
                                
                    # 转换为普通字典
                    combined[model][attribute][attr_value] = dict(combined[model][attribute][attr_value])
                    
        return combined
    
    def generate_summary_table(self, diversity_metrics, bias_metrics, hhi_metrics):
        """生成汇总表格"""
        table_data = []
        
        for model in self.models:
            model_row = {'Model': model}
            
            for attribute in self.sensitive_attributes.keys():
                if model in diversity_metrics and attribute in diversity_metrics[model]:
                    # 计算该属性的平均多样性、偏见和HHI
                    diversity_values = list(diversity_metrics[model][attribute].values())
                    bias_values = list(bias_metrics[model][attribute].values())
                    hhi_values = list(hhi_metrics[model][attribute].values())
                    
                    avg_diversity = np.mean(diversity_values) if diversity_values else 0.0
                    avg_bias = np.mean(bias_values) if bias_values else 0.0
                    avg_hhi = np.mean(hhi_values) if hhi_values else 0.0
                    
                    model_row[f'{attribute}_diversity'] = avg_diversity
                    model_row[f'{attribute}_bias'] = avg_bias
                    model_row[f'{attribute}_hhi'] = avg_hhi
                else:
                    model_row[f'{attribute}_diversity'] = 0.0
                    model_row[f'{attribute}_bias'] = 0.0
                    model_row[f'{attribute}_hhi'] = 0.0
            
            table_data.append(model_row)
        
        return pd.DataFrame(table_data)
    
    def save_results(self, results: Dict, output_dir: str = 'analyze'):
        """保存分析结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存汇总表格
        summary_table = results['summary_table']
        summary_table.to_csv(f'{output_dir}/improved_sensitive_attribute_model_summary.csv', index=False)
        
        # 保存详细指标
        detailed_data = []
        
        for model in results['diversity_metrics']:
            for attribute in results['diversity_metrics'][model]:
                for attr_value in results['diversity_metrics'][model][attribute]:
                    detailed_data.append({
                        'model': model,
                        'attribute': attribute,
                        'attribute_value': attr_value,
                        'diversity_score': results['diversity_metrics'][model][attribute][attr_value],
                        'bias_score': results['bias_metrics'][model][attribute][attr_value],
                        'hhi_score': results['hhi_metrics'][model][attribute][attr_value]
                    })
        
        detailed_df = pd.DataFrame(detailed_data)
        detailed_df.to_csv(f'{output_dir}/improved_detailed_metrics.csv', index=False)
        
        # 保存组合结果
        combined_data = []
        for model in results['combined_results']:
            for attribute in results['combined_results'][model]:
                for attr_value in results['combined_results'][model][attribute]:
                    for brand, score in results['combined_results'][model][attribute][attr_value].items():
                        combined_data.append({
                            'model': model,
                            'attribute': attribute,
                            'attribute_value': attr_value,
                            'brand': brand,
                            'combined_score': score
                        })
        
        combined_df = pd.DataFrame(combined_data)
        combined_df.to_csv(f'{output_dir}/improved_combined_results.csv', index=False)
        
        print(f"\n改进版分析结果已保存到 {output_dir}/ 目录")
        print(f"- 汇总表格: improved_sensitive_attribute_model_summary.csv")
        print(f"- 详细指标: improved_detailed_metrics.csv") 
        print(f"- 组合结果: improved_combined_results.csv")


def main():
    """主函数"""
    print("=== 敏感属性与模型选择组合分析 (改进版) ===")
    
    analyzer = ImprovedSensitiveAttributeAnalyzer()
    results = analyzer.run_analysis()
    
    print("\n=== 分析结果汇总表格 ===")
    summary_table = results['summary_table']
    print(summary_table.to_string(index=False, float_format='%.4f'))
    
    # 保存结果
    analyzer.save_results(results)
    
    print("\n=== 分析完成 ===")
    print("\n指标说明:")
    print("- diversity: Shannon多样性指数，值越高表示品牌推荐越多样化")
    print("- bias: KL散度，值越高表示该属性值的品牌偏好与该属性内全局平均差异越大")
    print("- hhi: Herfindahl-Hirschman Index，值越高表示品牌推荐越集中（多样性越低）")
    

if __name__ == "__main__":
    main()
