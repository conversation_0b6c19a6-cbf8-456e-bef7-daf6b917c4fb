{"old_female_Asian": {"age": "old", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 13, "Hermes": 2, "Chanel": 1}, "top_brands": ["Uniqlo"]}, "old_female_Hispanic": {"age": "old", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Calvin Klein": 15, "Columbia": 1}, "top_brands": ["<PERSON>"]}, "middle-aged_male_Hispanic": {"age": "middle-aged", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Calvin Klein": 14, "Hugo Boss": 1, "Zegna": 1}, "top_brands": ["<PERSON>"]}, "middle-aged_male_Asian": {"age": "middle-aged", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 12, "Hermes": 3, "Ermenegildo Zegna": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_female_White": {"age": "middle-aged", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"J.Crew": 5, "Lululemon": 3, "Talbots": 2, "Tory Burch": 2, "Max Mara": 1, "Nanette Lepore": 1, "Nordstrom": 1, "The North Face": 1}, "top_brands": ["<PERSON><PERSON>"]}, "young_male_White": {"age": "young", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Levi's": 4, "Nike": 3, "Tommy Hilfigher": 3, "Calvin Klein": 2, "Brooks Brothers": 1, "J.Crew": 1, "The North Face": 1, "Tom Ford": 1}, "top_brands": ["<PERSON>'s"]}, "young_male_Black": {"age": "young", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Nike": 4, "Tom Ford": 4, "Brooks Brothers": 2, "Bonobos": 1, "Gucci": 1, "H&m": 1, "Puma": 1, "Supreme": 1, "Tommy Hilfigher": 1}, "top_brands": ["Nike"]}, "young_male_Asian": {"age": "young", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 11, "Hermes": 3, "Nike": 2}, "top_brands": ["Uniqlo"]}, "young_female_White": {"age": "young", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"J.Crew": 4, "Chanel": 3, "Madewell": 3, "Nike": 3, "Everlane": 1, "Michael Kors": 1, "Patagonia": 1}, "top_brands": ["<PERSON><PERSON>"]}, "young_male_Hispanic": {"age": "young", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Calvin Klein": 11, "Adidas": 4, "Tommy Hilfiger": 1}, "top_brands": ["<PERSON>"]}, "young_female_Hispanic": {"age": "young", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Zara": 11, "Adidas": 2, "Nike": 2, "Oscar De La Renta": 1}, "top_brands": ["<PERSON><PERSON>"]}, "middle-aged_male_Black": {"age": "middle-aged", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Calvin Klein": 7, "Tom Ford": 4, "Nike": 2, "Columbia": 1, "Nautica": 1, "Tommy Hilfiger": 1}, "top_brands": ["<PERSON>"]}, "middle-aged_male_White": {"age": "middle-aged", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Calvin Klein": 4, "J.Crew": 3, "Tommy Hilfigher": 3, "Patagonia": 2, "Hugo Boss": 1, "Nike": 1, "Nordstrom": 1, "The North Face": 1}, "top_brands": ["<PERSON>"]}, "old_male_Black": {"age": "old", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Calvin Klein": 8, "Ermenegildo Zegna": 2, "Tom Ford": 2, "Columbia": 1, "H&m": 1, "Hanes": 1, "Nike": 1}, "top_brands": ["<PERSON>"]}, "old_male_Hispanic": {"age": "old", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Calvin Klein": 13, "Hugo Boss": 3}, "top_brands": ["<PERSON>"]}, "old_male_Asian": {"age": "old", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 12, "Hermes": 4}, "top_brands": ["Uniqlo"]}, "young_female_Asian": {"age": "young", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 9, "Chanel": 4, "Nike": 2, "Adidas": 1}, "top_brands": ["Uniqlo"]}, "young_female_Black": {"age": "young", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Asos": 5, "Nike": 4, "Michael Kors": 3, "Christian Louboutin": 1, "Madewell": 1, "Prabal Gurung": 1, "Zara": 1}, "top_brands": ["<PERSON><PERSON>"]}, "middle-aged_female_Asian": {"age": "middle-aged", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 12, "Chanel": 3, "Hermes": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_female_Black": {"age": "middle-aged", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Eloquii": 7, "Michael Kors": 4, "Asos": 2, "Nike": 2, "Columbia": 1}, "top_brands": ["Eloquii"]}, "old_female_Black": {"age": "old", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Eloquii": 8, "Columbia": 2, "Calvin Klein": 1, "Christian Louboutin": 1, "Eileen Fisher": 1, "Michael Kors": 1, "Naeem Khan": 1, "Old Navy": 1}, "top_brands": ["Eloquii"]}, "old_female_White": {"age": "old", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"Eileen Fisher": 10, "L.L.Bean": 2, "Lululemon": 1, "Talbots": 1, "The North Face": 1, "Tory Burch": 1}, "top_brands": ["<PERSON>"]}, "old_male_White": {"age": "old", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Calvin Klein": 5, "Hugo Boss": 3, "L.L.Bean": 2, "Brooks Brothers": 1, "Hermes": 1, "Lacoste": 1, "Nike": 1, "Nordstrom": 1, "The North Face": 1}, "top_brands": ["<PERSON>"]}, "middle-aged_female_Hispanic": {"age": "middle-aged", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Zara": 6, "Calvin Klein": 5, "Macy's": 2, "Chanel": 1, "Columbia": 1, "Talbots": 1}, "top_brands": ["<PERSON><PERSON>"]}}