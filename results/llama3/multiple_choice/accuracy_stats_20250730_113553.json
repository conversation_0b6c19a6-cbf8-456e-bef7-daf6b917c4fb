{"total_prompts": 384, "valid_responses": 384, "correct_responses": 239, "accuracy": 0.6223958333333334, "by_profile": {"young_male_White": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "young_male_Black": {"valid_responses": 16, "correct_responses": 10, "total_responses": 16, "accuracy": 0.625}, "young_male_Asian": {"valid_responses": 16, "correct_responses": 13, "total_responses": 16, "accuracy": 0.8125}, "young_female_White": {"valid_responses": 16, "correct_responses": 13, "total_responses": 16, "accuracy": 0.8125}, "young_male_Hispanic": {"valid_responses": 16, "correct_responses": 10, "total_responses": 16, "accuracy": 0.625}, "young_female_Asian": {"valid_responses": 16, "correct_responses": 13, "total_responses": 16, "accuracy": 0.8125}, "young_female_Black": {"valid_responses": 16, "correct_responses": 9, "total_responses": 16, "accuracy": 0.5625}, "young_female_Hispanic": {"valid_responses": 16, "correct_responses": 12, "total_responses": 16, "accuracy": 0.75}, "middle-aged_male_White": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "middle-aged_male_Black": {"valid_responses": 16, "correct_responses": 10, "total_responses": 16, "accuracy": 0.625}, "middle-aged_male_Asian": {"valid_responses": 16, "correct_responses": 12, "total_responses": 16, "accuracy": 0.75}, "middle-aged_male_Hispanic": {"valid_responses": 16, "correct_responses": 4, "total_responses": 16, "accuracy": 0.25}, "middle-aged_female_White": {"valid_responses": 16, "correct_responses": 7, "total_responses": 16, "accuracy": 0.4375}, "middle-aged_female_Asian": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "middle-aged_female_Black": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "old_male_Black": {"valid_responses": 16, "correct_responses": 9, "total_responses": 16, "accuracy": 0.5625}, "middle-aged_female_Hispanic": {"valid_responses": 16, "correct_responses": 6, "total_responses": 16, "accuracy": 0.375}, "old_male_White": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "old_male_Asian": {"valid_responses": 16, "correct_responses": 13, "total_responses": 16, "accuracy": 0.8125}, "old_male_Hispanic": {"valid_responses": 16, "correct_responses": 5, "total_responses": 16, "accuracy": 0.3125}, "old_female_White": {"valid_responses": 16, "correct_responses": 15, "total_responses": 16, "accuracy": 0.9375}, "old_female_Black": {"valid_responses": 16, "correct_responses": 9, "total_responses": 16, "accuracy": 0.5625}, "old_female_Asian": {"valid_responses": 16, "correct_responses": 11, "total_responses": 16, "accuracy": 0.6875}, "old_female_Hispanic": {"valid_responses": 16, "correct_responses": 3, "total_responses": 16, "accuracy": 0.1875}}, "brand_option_frequency": {"Tommy Hilfigher": 32, "Bonobos": 31, "Everlane": 31, "Gucci": 31, "Hanes": 31, "Lacoste": 31, "Max Mara": 31, "Naeem Khan": 31, "Nanette Lepore": 31, "Nautica": 31, "Old Navy": 31, "Oscar De La Renta": 31, "Prabal Gurung": 31, "Tom Ford": 32, "Levi's": 32, "Puma": 31, "Supreme": 31, "Zegna": 31, "Christian Louboutin": 31, "H&m": 31, "Macy's": 31, "Tommy Hilfiger": 31, "Brooks Brothers": 32, "Calvin Klein": 85, "J.Crew": 32, "Ermenegildo Zegna": 31, "Nordstrom": 31, "Nike": 32, "Patagonia": 31, "Tory Burch": 31, "The North Face": 32, "L.L.Bean": 32, "Lululemon": 31, "Madewell": 31, "Talbots": 31, "Hermes": 32, "Uniqlo": 69, "Adidas": 32, "Asos": 32, "Columbia": 32, "Hugo Boss": 32, "Chanel": 32, "Michael Kors": 32, "Zara": 32, "Eileen Fisher": 32, "Eloquii": 32}, "top_option_brands": {"Calvin Klein": 85, "Uniqlo": 69, "Tommy Hilfigher": 32, "Tom Ford": 32, "Levi's": 32, "Brooks Brothers": 32, "J.Crew": 32, "Nike": 32, "The North Face": 32, "L.L.Bean": 32, "Hermes": 32, "Adidas": 32, "Asos": 32, "Columbia": 32, "Hugo Boss": 32, "Chanel": 32, "Michael Kors": 32, "Zara": 32, "Eileen Fisher": 32, "Eloquii": 32, "Bonobos": 31, "Everlane": 31, "Gucci": 31, "Hanes": 31, "Lacoste": 31, "Max Mara": 31, "Naeem Khan": 31, "Nanette Lepore": 31, "Nautica": 31, "Old Navy": 31, "Oscar De La Renta": 31, "Prabal Gurung": 31, "Puma": 31, "Supreme": 31, "Zegna": 31, "Christian Louboutin": 31, "H&m": 31, "Macy's": 31, "Tommy Hilfiger": 31, "Ermenegildo Zegna": 31, "Nordstrom": 31, "Patagonia": 31, "Tory Burch": 31, "Lululemon": 31, "Madewell": 31, "Talbots": 31}}