#!/usr/bin/env python3
"""
Direct任务全局公平性指标生成工具
生成不依赖敏感属性划分的系统级评估表格
"""

import os
import argparse
import pandas as pd
from global_fairness_metrics_v2 import GlobalFairnessMetrics

def create_global_metrics_table(model_name, output_dir="global_metrics_results"):
    """为单个模型创建全局指标表格"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建结果文件路径
    results_file = f"results/{model_name}/direct"
    
    if os.path.exists(results_file):
        files = [f for f in os.listdir(results_file) if f.startswith('final_') and f.endswith('.csv')]
        if files:
            latest_file = sorted(files)[-1]
            results_path = os.path.join(results_file, latest_file)
            print(f"处理 {model_name} 模型数据: {results_path}")
        else:
            print(f"未找到 {model_name} 模型的结果文件")
            return None
    else:
        print(f"结果目录不存在: {results_file}")
        return None
    
    try:
        # 加载数据
        results_df = pd.read_csv(results_path)
        
        # 初始化分析器
        analyzer = GlobalFairnessMetrics(results_df)
        
        # 生成报告
        report = analyzer.generate_comprehensive_report()
        
        # 提取关键指标
        ofi = report['overall_fairness_index']
        components = report['component_analysis']
        details = report['detailed_metrics']
        
        global_metrics = {
            'Model': model_name,
            'OFI_Score': round(ofi, 4),
            'Fairness_Grade': report['fairness_grade'],
            'Brand_Diversity_Ratio': round(details['global_brand_diversity']['global_brand_diversity_ratio'], 4),
            'Population_Consistency': round(details['population_consistency']['population_consistency_index'], 4),
            'Distribution_Evenness': round(details['brand_distribution_evenness']['brand_distribution_evenness'], 4),
            'Bias_Control': round(components['bias_control'], 4),
            'Balance_Score': round(details['recommendation_balance']['recommendation_balance_score'], 4),
            'Unique_Brands': details['global_brand_diversity']['unique_brands'],
            'Total_Responses': details['global_brand_diversity']['total_responses'],
            'High_Bias_Populations': details['systemic_deviation']['high_deviation_count'],
            'Total_Populations': details['systemic_deviation']['total_populations'],
            'Top_Brand': details['brand_distribution_evenness']['most_recommended_brand'],
            'Top_Brand_Percentage': round(details['brand_distribution_evenness']['most_recommended_percentage'], 1),
            'Effective_Brand_Number': round(details['recommendation_balance']['effective_brand_number'], 1),
            'Systemic_Deviation': round(details['systemic_deviation']['systemic_deviation_magnitude'], 4)
        }
        
        return global_metrics
        
    except Exception as e:
        print(f"处理 {model_name} 时出错: {e}")
        return None

def create_multi_model_comparison(models, output_dir="global_metrics_results"):
    """创建多模型全局指标对比表"""
    
    all_metrics = []
    
    for model in models:
        metrics = create_global_metrics_table(model, output_dir)
        if metrics:
            all_metrics.append(metrics)
    
    if all_metrics:
        # 创建对比表格
        df = pd.DataFrame(all_metrics)
        
        # 保存完整表格
        full_table_file = os.path.join(output_dir, "global_fairness_comparison.csv")
        df.to_csv(full_table_file, index=False)
        print(f"完整对比表已保存: {full_table_file}")
        
        # 创建核心指标表格
        core_metrics = df[['Model', 'OFI_Score', 'Fairness_Grade', 
                          'Brand_Diversity_Ratio', 'Population_Consistency', 
                          'Distribution_Evenness', 'Bias_Control', 'Balance_Score']]
        
        core_table_file = os.path.join(output_dir, "core_fairness_metrics.csv")
        core_metrics.to_csv(core_table_file, index=False)
        print(f"核心指标表已保存: {core_table_file}")
        
        # 创建简化摘要表格
        summary_metrics = df[['Model', 'OFI_Score', 'Fairness_Grade', 
                             'Unique_Brands', 'High_Bias_Populations', 
                             'Top_Brand', 'Top_Brand_Percentage']]
        
        summary_table_file = os.path.join(output_dir, "fairness_summary.csv")
        summary_metrics.to_csv(summary_table_file, index=False)
        print(f"摘要表格已保存: {summary_table_file}")
        
        # 打印结果摘要
        print("\n=== 模型全局公平性对比摘要 ===")
        print(f"{'模型':<12} {'OFI评分':<8} {'等级':<12} {'品牌数':<6} {'高偏差人群':<10} {'主要品牌':<15}")
        print("-" * 70)
        
        for _, row in df.iterrows():
            print(f"{row['Model']:<12} {row['OFI_Score']:<8} {row['Fairness_Grade']:<12} "
                  f"{row['Unique_Brands']:<6} {row['High_Bias_Populations']}/{row['Total_Populations']:<8} "
                  f"{row['Top_Brand']:<15}")
        
        # 按OFI排序显示最佳和最差模型
        df_sorted = df.sort_values('OFI_Score', ascending=False)
        print(f"\n🏆 公平性最佳: {df_sorted.iloc[0]['Model']} (OFI: {df_sorted.iloc[0]['OFI_Score']})")
        print(f"⚠️  公平性最差: {df_sorted.iloc[-1]['Model']} (OFI: {df_sorted.iloc[-1]['OFI_Score']})")
        
        return df
    
    else:
        print("未能获取任何模型的指标数据")
        return None

def main():
    parser = argparse.ArgumentParser(description='生成全局公平性指标表格')
    parser.add_argument('--model', type=str, 
                       help='分析单个模型 (qwen/llama/mistral/deepseek/gemma/glm)')
    parser.add_argument('--all', action='store_true',
                       help='分析所有可用模型并生成对比表')
    parser.add_argument('--models', nargs='+', 
                       help='分析指定的多个模型')
    parser.add_argument('--output_dir', type=str, default='global_metrics_results',
                       help='输出目录')
    
    args = parser.parse_args()
    
    if args.model:
        # 分析单个模型
        metrics = create_global_metrics_table(args.model, args.output_dir)
        if metrics:
            print(f"\n=== {args.model.upper()} 模型全局公平性指标 ===")
            print(f"整体公平性指数: {metrics['OFI_Score']}")
            print(f"公平性等级: {metrics['Fairness_Grade']}")
            print(f"品牌多样性比率: {metrics['Brand_Diversity_Ratio']}")
            print(f"人群一致性: {metrics['Population_Consistency']}")
            print(f"分布均匀度: {metrics['Distribution_Evenness']}")
            print(f"偏差控制: {metrics['Bias_Control']}")
            print(f"平衡评分: {metrics['Balance_Score']}")
            
            # 保存单模型结果
            df = pd.DataFrame([metrics])
            single_file = os.path.join(args.output_dir, f"{args.model}_global_metrics.csv")
            df.to_csv(single_file, index=False)
            print(f"\n结果已保存: {single_file}")
    
    elif args.all:
        # 分析所有模型
        all_models = ['qwen', 'llama', 'mistral', 'deepseek', 'gemma', 'glm']
        create_multi_model_comparison(all_models, args.output_dir)
    
    elif args.models:
        # 分析指定模型
        create_multi_model_comparison(args.models, args.output_dir)
    
    else:
        print("请指定 --model, --models 或 --all 参数")

if __name__ == "__main__":
    main()
